import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'dart:async';
import 'package:hia_sang_ma/screens/chat_screen.dart';
import 'package:hia_sang_ma/services/chat_service.dart';
import 'package:hia_sang_ma/services/unread_message_service.dart';
import 'package:hia_sang_ma/services/chat_socket_service.dart';
import 'package:hia_sang_ma/models/chat_model.dart';

// Generate mocks
@GenerateMocks([ChatService, UnreadMessageService, ChatSocketService])
import 'chat_notification_test.mocks.dart';

void main() {
  group('Chat Notification Functionality Tests', () {
    late MockChatService mockChatService;
    late MockUnreadMessageService mockUnreadMessageService;
    late MockChatSocketService mockChatSocketService;
    late StreamController<Map<String, dynamic>> notificationController;

    setUp(() {
      mockChatService = MockChatService();
      mockUnreadMessageService = MockUnreadMessageService();
      mockChatSocketService = MockChatSocketService();
      notificationController =
          StreamController<Map<String, dynamic>>.broadcast();

      // Setup default mock responses
      when(mockChatService.getAllChatsGroupedByType()).thenAnswer(
        (_) async => {
          ChatType.private: <ChatModel>[],
          ChatType.task: <ChatModel>[],
          ChatType.department: <ChatModel>[],
          ChatType.organization: <ChatModel>[],
        },
      );

      when(mockUnreadMessageService.getUnreadCountsByType()).thenReturn({
        ChatType.private: 0,
        ChatType.task: 0,
        ChatType.department: 0,
        ChatType.organization: 0,
      });

      when(mockUnreadMessageService.unreadCountsByTypeStream).thenAnswer(
        (_) => Stream.value({
          ChatType.private: 0,
          ChatType.task: 0,
          ChatType.department: 0,
          ChatType.organization: 0,
        }),
      );

      when(
        mockChatSocketService.chatNotificationStream,
      ).thenAnswer((_) => notificationController.stream);
    });

    tearDown(() {
      notificationController.close();
    });

    group('Socket Listener Setup', () {
      testWidgets('should set up chat notification listener on init', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

        await tester.pumpAndSettle();

        // Verify that setupChatEventListeners was called
        verify(mockChatSocketService.setupChatEventListeners()).called(1);
      });

      testWidgets('should dispose socket service on widget dispose', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

        await tester.pumpAndSettle();

        // Navigate away to trigger dispose
        await tester.pumpWidget(MaterialApp(home: Container()));

        await tester.pumpAndSettle();

        // Verify that dispose was called
        verify(mockChatSocketService.dispose()).called(1);
      });
    });

    group('Notification Handling', () {
      testWidgets('should handle new_message notification', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

        await tester.pumpAndSettle();

        // Simulate new message notification
        notificationController.add({
          'type': 'new_message',
          'chatId': 123,
          'chatType': 'PRIVATE',
          'messageId': 456,
        });

        await tester.pump();

        // Verify that data refresh was triggered
        // Note: In a real implementation, we would verify the service calls
        expect(find.byType(ChatScreen), findsOneWidget);
      });

      testWidgets('should handle chat_updated notification', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

        await tester.pumpAndSettle();

        // Simulate chat updated notification
        notificationController.add({
          'type': 'chat_updated',
          'chatId': 123,
          'chatType': 'TASK',
        });

        await tester.pump();

        expect(find.byType(ChatScreen), findsOneWidget);
      });

      testWidgets('should handle user_joined notification', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

        await tester.pumpAndSettle();

        // Simulate user joined notification
        notificationController.add({
          'type': 'user_joined',
          'chatId': 123,
          'userId': 789,
          'chatType': 'DEPARTMENT',
        });

        await tester.pump();

        expect(find.byType(ChatScreen), findsOneWidget);
      });

      testWidgets('should handle unknown notification types gracefully', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

        await tester.pumpAndSettle();

        // Simulate unknown notification type
        notificationController.add({
          'type': 'unknown_type',
          'chatId': 123,
          'someData': 'test',
        });

        await tester.pump();

        // Should not crash and still display the screen
        expect(find.byType(ChatScreen), findsOneWidget);
      });

      testWidgets('should handle malformed notification data', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

        await tester.pumpAndSettle();

        // Simulate malformed notification
        notificationController.add({'invalid': 'data', 'missing': 'type'});

        await tester.pump();

        // Should not crash
        expect(find.byType(ChatScreen), findsOneWidget);
      });
    });

    group('UI Feedback', () {
      testWidgets('should show loading indicator during notification refresh', (
        WidgetTester tester,
      ) async {
        // Setup delayed response to simulate loading
        when(mockChatService.getAllChatsGroupedByType()).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return {
            ChatType.private: <ChatModel>[],
            ChatType.task: <ChatModel>[],
            ChatType.department: <ChatModel>[],
            ChatType.organization: <ChatModel>[],
          };
        });

        await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

        await tester.pumpAndSettle();

        // Trigger notification
        notificationController.add({'type': 'new_message', 'chatId': 123});

        await tester.pump();

        // Should show loading indicator in AppBar
        // Note: This would need to be verified with the actual implementation
        expect(find.byType(ChatScreen), findsOneWidget);
      });

      testWidgets('should show snackbar feedback for refresh', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

        await tester.pumpAndSettle();

        // Trigger notification
        notificationController.add({'type': 'new_message', 'chatId': 123});

        await tester.pump();
        await tester.pump(const Duration(milliseconds: 100));

        // Should show snackbar with refresh message
        // Note: This would need to be verified with the actual implementation
        expect(find.byType(ChatScreen), findsOneWidget);
      });
    });

    group('Error Handling', () {
      testWidgets('should handle service errors gracefully', (
        WidgetTester tester,
      ) async {
        // Setup service to throw error
        when(
          mockChatService.getAllChatsGroupedByType(),
        ).thenThrow(Exception('Network error'));

        await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

        await tester.pumpAndSettle();

        // Trigger notification
        notificationController.add({'type': 'new_message', 'chatId': 123});

        await tester.pump();

        // Should not crash and show error feedback
        expect(find.byType(ChatScreen), findsOneWidget);
      });

      testWidgets('should handle stream errors', (WidgetTester tester) async {
        await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

        await tester.pumpAndSettle();

        // Simulate stream error
        notificationController.addError('Stream error');

        await tester.pump();

        // Should not crash
        expect(find.byType(ChatScreen), findsOneWidget);
      });
    });

    group('Performance', () {
      testWidgets('should not refresh if widget is not mounted', (
        WidgetTester tester,
      ) async {
        await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

        await tester.pumpAndSettle();

        // Navigate away
        await tester.pumpWidget(MaterialApp(home: Container()));

        // Trigger notification after navigation
        notificationController.add({'type': 'new_message', 'chatId': 123});

        await tester.pump();

        // Should not cause issues
        expect(find.byType(Container), findsOneWidget);
      });
    });
  });

  group('Integration Tests', () {
    testWidgets('should complete full notification refresh cycle', (
      WidgetTester tester,
    ) async {
      // Track service calls
      var chatServiceCalls = 0;
      var unreadServiceCalls = 0;

      when(mockChatService.getAllChatsGroupedByType()).thenAnswer((_) async {
        chatServiceCalls++;
        return {
          ChatType.private: <ChatModel>[],
          ChatType.task: <ChatModel>[],
          ChatType.department: <ChatModel>[],
          ChatType.organization: <ChatModel>[],
        };
      });

      when(mockUnreadMessageService.refreshAllUnreadCounts()).thenAnswer((
        _,
      ) async {
        unreadServiceCalls++;
      });

      await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

      await tester.pumpAndSettle();

      // Reset counters after initial load
      chatServiceCalls = 0;
      unreadServiceCalls = 0;

      // Trigger notification
      notificationController.add({
        'type': 'new_message',
        'chatId': 123,
        'chatType': 'PRIVATE',
      });

      await tester.pump();
      await tester.pumpAndSettle();

      // Verify both services were called for refresh
      expect(chatServiceCalls, greaterThan(0));
      expect(unreadServiceCalls, greaterThan(0));
    });

    testWidgets('should handle multiple rapid notifications', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(MaterialApp(home: const ChatScreen()));

      await tester.pumpAndSettle();

      // Send multiple notifications rapidly
      for (int i = 0; i < 5; i++) {
        notificationController.add({'type': 'new_message', 'chatId': 123 + i});
      }

      await tester.pump();
      await tester.pumpAndSettle();

      // Should handle all notifications without crashing
      expect(find.byType(ChatScreen), findsOneWidget);
    });
  });
}
