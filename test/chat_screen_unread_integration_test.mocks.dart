// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in hia_sang_ma/test/chat_screen_unread_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:hia_sang_ma/models/chat_model.dart' as _i4;
import 'package:hia_sang_ma/services/chat_service.dart' as _i2;
import 'package:hia_sang_ma/services/unread_message_service.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [ChatService].
///
/// See the documentation for Mockito's code generation for more information.
class MockChatService extends _i1.Mock implements _i2.ChatService {
  MockChatService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<List<_i4.ChatModel>> getChats({
    int? id,
    _i4.ChatType? chatType,
    int? userId,
    int? organizationId,
    int? departmentId,
    int? taskId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getChats, [], {
              #id: id,
              #chatType: chatType,
              #userId: userId,
              #organizationId: organizationId,
              #departmentId: departmentId,
              #taskId: taskId,
            }),
            returnValue: _i3.Future<List<_i4.ChatModel>>.value(
              <_i4.ChatModel>[],
            ),
          )
          as _i3.Future<List<_i4.ChatModel>>);

  @override
  _i3.Future<_i4.ChatModel?> getChatById(int? chatId) =>
      (super.noSuchMethod(
            Invocation.method(#getChatById, [chatId]),
            returnValue: _i3.Future<_i4.ChatModel?>.value(),
          )
          as _i3.Future<_i4.ChatModel?>);

  @override
  _i3.Future<List<_i4.ChatMessage>> getChatMessages({
    required int? chatId,
    int? messageId,
    int? page = 1,
    int? limit = 20,
    int? before,
    int? after,
    bool? includeReadStatus = false,
    String? orderBy = 'created',
    String? orderDirection = 'DESC',
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getChatMessages, [], {
              #chatId: chatId,
              #messageId: messageId,
              #page: page,
              #limit: limit,
              #before: before,
              #after: after,
              #includeReadStatus: includeReadStatus,
              #orderBy: orderBy,
              #orderDirection: orderDirection,
            }),
            returnValue: _i3.Future<List<_i4.ChatMessage>>.value(
              <_i4.ChatMessage>[],
            ),
          )
          as _i3.Future<List<_i4.ChatMessage>>);

  @override
  _i3.Future<int> getUnreadMessageCountForChat(int? chatId) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadMessageCountForChat, [chatId]),
            returnValue: _i3.Future<int>.value(0),
          )
          as _i3.Future<int>);

  @override
  _i3.Future<List<_i4.UnreadChatInfo>> getAllUnreadMessageCounts() =>
      (super.noSuchMethod(
            Invocation.method(#getAllUnreadMessageCounts, []),
            returnValue: _i3.Future<List<_i4.UnreadChatInfo>>.value(
              <_i4.UnreadChatInfo>[],
            ),
          )
          as _i3.Future<List<_i4.UnreadChatInfo>>);

  @override
  _i3.Future<int> getTotalUnreadMessageCount() =>
      (super.noSuchMethod(
            Invocation.method(#getTotalUnreadMessageCount, []),
            returnValue: _i3.Future<int>.value(0),
          )
          as _i3.Future<int>);

  @override
  _i3.Future<int> getUnreadMessageCount() =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadMessageCount, []),
            returnValue: _i3.Future<int>.value(0),
          )
          as _i3.Future<int>);

  @override
  _i3.Future<Map<String, dynamic>> markMessagesAsRead({
    int? messageId,
    int? chatId,
    int? beforeMessageId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#markMessagesAsRead, [], {
              #messageId: messageId,
              #chatId: chatId,
              #beforeMessageId: beforeMessageId,
            }),
            returnValue: _i3.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i3.Future<Map<String, dynamic>>);

  @override
  _i3.Future<Map<_i4.ChatType, int>> getUnreadCountsByType() =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCountsByType, []),
            returnValue: _i3.Future<Map<_i4.ChatType, int>>.value(
              <_i4.ChatType, int>{},
            ),
          )
          as _i3.Future<Map<_i4.ChatType, int>>);

  @override
  _i3.Future<List<_i4.ChatModel>> getChatsByType(_i4.ChatType? chatType) =>
      (super.noSuchMethod(
            Invocation.method(#getChatsByType, [chatType]),
            returnValue: _i3.Future<List<_i4.ChatModel>>.value(
              <_i4.ChatModel>[],
            ),
          )
          as _i3.Future<List<_i4.ChatModel>>);

  @override
  _i3.Future<Map<_i4.ChatType, List<_i4.ChatModel>>>
  getAllChatsGroupedByType() =>
      (super.noSuchMethod(
            Invocation.method(#getAllChatsGroupedByType, []),
            returnValue:
                _i3.Future<Map<_i4.ChatType, List<_i4.ChatModel>>>.value(
                  <_i4.ChatType, List<_i4.ChatModel>>{},
                ),
          )
          as _i3.Future<Map<_i4.ChatType, List<_i4.ChatModel>>>);
}

/// A class which mocks [UnreadMessageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockUnreadMessageService extends _i1.Mock
    implements _i5.UnreadMessageService {
  MockUnreadMessageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Stream<Map<int, int>> get unreadCountsStream =>
      (super.noSuchMethod(
            Invocation.getter(#unreadCountsStream),
            returnValue: _i3.Stream<Map<int, int>>.empty(),
          )
          as _i3.Stream<Map<int, int>>);

  @override
  _i3.Stream<Map<_i4.ChatType, int>> get unreadCountsByTypeStream =>
      (super.noSuchMethod(
            Invocation.getter(#unreadCountsByTypeStream),
            returnValue: _i3.Stream<Map<_i4.ChatType, int>>.empty(),
          )
          as _i3.Stream<Map<_i4.ChatType, int>>);

  @override
  _i3.Stream<int> get totalUnreadCountStream =>
      (super.noSuchMethod(
            Invocation.getter(#totalUnreadCountStream),
            returnValue: _i3.Stream<int>.empty(),
          )
          as _i3.Stream<int>);

  @override
  _i3.Stream<_i5.UnreadCountUpdate> get unreadCountUpdateStream =>
      (super.noSuchMethod(
            Invocation.getter(#unreadCountUpdateStream),
            returnValue: _i3.Stream<_i5.UnreadCountUpdate>.empty(),
          )
          as _i3.Stream<_i5.UnreadCountUpdate>);

  @override
  _i3.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<int> getUnreadCountForChat(int? chatId) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCountForChat, [chatId]),
            returnValue: _i3.Future<int>.value(0),
          )
          as _i3.Future<int>);

  @override
  int getUnreadCountForType(_i4.ChatType? chatType) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCountForType, [chatType]),
            returnValue: 0,
          )
          as int);

  @override
  int getTotalUnreadCount() =>
      (super.noSuchMethod(
            Invocation.method(#getTotalUnreadCount, []),
            returnValue: 0,
          )
          as int);

  @override
  Map<int, int> getAllUnreadCounts() =>
      (super.noSuchMethod(
            Invocation.method(#getAllUnreadCounts, []),
            returnValue: <int, int>{},
          )
          as Map<int, int>);

  @override
  Map<_i4.ChatType, int> getUnreadCountsByType() =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCountsByType, []),
            returnValue: <_i4.ChatType, int>{},
          )
          as Map<_i4.ChatType, int>);

  @override
  _i3.Future<void> refreshAllUnreadCounts() =>
      (super.noSuchMethod(
            Invocation.method(#refreshAllUnreadCounts, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  void updateUnreadCountForChat(int? chatId, int? newCount) =>
      super.noSuchMethod(
        Invocation.method(#updateUnreadCountForChat, [chatId, newCount]),
        returnValueForMissingStub: null,
      );

  @override
  void updateUnreadCountForChatWithType(
    int? chatId,
    int? newCount,
    _i4.ChatType? chatType,
  ) => super.noSuchMethod(
    Invocation.method(#updateUnreadCountForChatWithType, [
      chatId,
      newCount,
      chatType,
    ]),
    returnValueForMissingStub: null,
  );

  @override
  _i3.Future<void> markMessagesAsRead({
    int? messageId,
    int? chatId,
    int? beforeMessageId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#markMessagesAsRead, [], {
              #messageId: messageId,
              #chatId: chatId,
              #beforeMessageId: beforeMessageId,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> clearCacheAndRefresh() =>
      (super.noSuchMethod(
            Invocation.method(#clearCacheAndRefresh, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}
