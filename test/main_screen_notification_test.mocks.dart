// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in hia_sang_ma/test/main_screen_notification_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:hia_sang_ma/models/chat_model.dart' as _i4;
import 'package:hia_sang_ma/services/unread_message_service.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [UnreadMessageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockUnreadMessageService extends _i1.Mock
    implements _i2.UnreadMessageService {
  MockUnreadMessageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Stream<Map<int, int>> get unreadCountsStream =>
      (super.noSuchMethod(
            Invocation.getter(#unreadCountsStream),
            returnValue: _i3.Stream<Map<int, int>>.empty(),
          )
          as _i3.Stream<Map<int, int>>);

  @override
  _i3.Stream<Map<_i4.ChatType, int>> get unreadCountsByTypeStream =>
      (super.noSuchMethod(
            Invocation.getter(#unreadCountsByTypeStream),
            returnValue: _i3.Stream<Map<_i4.ChatType, int>>.empty(),
          )
          as _i3.Stream<Map<_i4.ChatType, int>>);

  @override
  _i3.Stream<int> get totalUnreadCountStream =>
      (super.noSuchMethod(
            Invocation.getter(#totalUnreadCountStream),
            returnValue: _i3.Stream<int>.empty(),
          )
          as _i3.Stream<int>);

  @override
  _i3.Stream<_i2.UnreadCountUpdate> get unreadCountUpdateStream =>
      (super.noSuchMethod(
            Invocation.getter(#unreadCountUpdateStream),
            returnValue: _i3.Stream<_i2.UnreadCountUpdate>.empty(),
          )
          as _i3.Stream<_i2.UnreadCountUpdate>);

  @override
  _i3.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<int> getUnreadCountForChat(int? chatId) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCountForChat, [chatId]),
            returnValue: _i3.Future<int>.value(0),
          )
          as _i3.Future<int>);

  @override
  int getUnreadCountForType(_i4.ChatType? chatType) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCountForType, [chatType]),
            returnValue: 0,
          )
          as int);

  @override
  int getTotalUnreadCount() =>
      (super.noSuchMethod(
            Invocation.method(#getTotalUnreadCount, []),
            returnValue: 0,
          )
          as int);

  @override
  Map<int, int> getAllUnreadCounts() =>
      (super.noSuchMethod(
            Invocation.method(#getAllUnreadCounts, []),
            returnValue: <int, int>{},
          )
          as Map<int, int>);

  @override
  Map<_i4.ChatType, int> getUnreadCountsByType() =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCountsByType, []),
            returnValue: <_i4.ChatType, int>{},
          )
          as Map<_i4.ChatType, int>);

  @override
  _i3.Future<void> refreshAllUnreadCounts() =>
      (super.noSuchMethod(
            Invocation.method(#refreshAllUnreadCounts, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  void updateUnreadCountForChat(int? chatId, int? newCount) =>
      super.noSuchMethod(
        Invocation.method(#updateUnreadCountForChat, [chatId, newCount]),
        returnValueForMissingStub: null,
      );

  @override
  void updateUnreadCountForChatWithType(
    int? chatId,
    int? newCount,
    _i4.ChatType? chatType,
  ) => super.noSuchMethod(
    Invocation.method(#updateUnreadCountForChatWithType, [
      chatId,
      newCount,
      chatType,
    ]),
    returnValueForMissingStub: null,
  );

  @override
  _i3.Future<void> markMessagesAsRead({
    int? messageId,
    int? chatId,
    int? beforeMessageId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#markMessagesAsRead, [], {
              #messageId: messageId,
              #chatId: chatId,
              #beforeMessageId: beforeMessageId,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> clearCacheAndRefresh() =>
      (super.noSuchMethod(
            Invocation.method(#clearCacheAndRefresh, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}
