import 'package:flutter_test/flutter_test.dart';
import 'package:hia_sang_ma/models/chat_model.dart';
import 'package:hia_sang_ma/models/user_model.dart';

void main() {
  group('ChatModel Private Chat Display', () {
    test('should parse chatUsers from API response correctly', () {
      // Mock API response structure
      final apiResponse = {
        "id": 4,
        "name": "Tom Lucablock",
        "chatType": "PRIVATE",
        "isActive": true,
        "isBot": false,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "unreadCount": 0,
        "chatUsers": [
          {
            "id": 10,
            "chatId": 4,
            "userId": 2,
            "joinedAt": "2024-01-01T00:00:00Z",
            "user": {
              "id": 2,
              "firstName": "Owner",
              "lastName": "<PERSON>",
              "email": "<EMAIL>",
              "role": "owner",
              "imageUrl": null
            }
          },
          {
            "id": 11,
            "chatId": 4,
            "userId": 4,
            "joinedAt": "2024-01-01T00:00:00Z",
            "user": {
              "id": 4,
              "firstName": "Tom",
              "lastName": "Lucablock",
              "email": "<EMAIL>",
              "role": "member",
              "imageUrl": "https://example.com/image.png"
            }
          }
        ]
      };

      // Parse the chat model
      final chat = ChatModel.fromJson(apiResponse);

      // Verify basic properties
      expect(chat.id, equals(4));
      expect(chat.name, equals("Tom Lucablock"));
      expect(chat.chatType, equals(ChatType.private));
      expect(chat.participants?.length, equals(2));

      // Verify participants are parsed correctly
      final participant1 = chat.participants![0];
      expect(participant1.user.id, equals(2));
      expect(participant1.user.firstName, equals("Owner"));
      expect(participant1.user.lastName, equals("Luca"));
      expect(participant1.user.imageUrl, isNull);

      final participant2 = chat.participants![1];
      expect(participant2.user.id, equals(4));
      expect(participant2.user.firstName, equals("Tom"));
      expect(participant2.user.lastName, equals("Lucablock"));
      expect(participant2.user.imageUrl, equals("https://example.com/image.png"));
    });

    test('should handle empty chatUsers array', () {
      final apiResponse = {
        "id": 5,
        "name": "Empty Chat",
        "chatType": "PRIVATE",
        "isActive": true,
        "isBot": false,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "unreadCount": 0,
        "chatUsers": []
      };

      final chat = ChatModel.fromJson(apiResponse);

      expect(chat.id, equals(5));
      expect(chat.participants?.length, equals(0));
      expect(chat.displayName, equals("Empty Chat"));
    });

    test('should handle missing chatUsers field', () {
      final apiResponse = {
        "id": 6,
        "name": "No Users Chat",
        "chatType": "PRIVATE",
        "isActive": true,
        "isBot": false,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "unreadCount": 0
      };

      final chat = ChatModel.fromJson(apiResponse);

      expect(chat.id, equals(6));
      expect(chat.participants, isNull);
      expect(chat.displayName, equals("No Users Chat"));
    });

    test('should use custom name when provided', () {
      final apiResponse = {
        "id": 7,
        "name": "Custom Chat Name",
        "chatType": "PRIVATE",
        "isActive": true,
        "isBot": false,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "unreadCount": 0,
        "chatUsers": [
          {
            "id": 12,
            "chatId": 7,
            "userId": 3,
            "joinedAt": "2024-01-01T00:00:00Z",
            "user": {
              "id": 3,
              "firstName": "John",
              "lastName": "Doe",
              "email": "<EMAIL>",
              "role": "member",
              "imageUrl": null
            }
          }
        ]
      };

      final chat = ChatModel.fromJson(apiResponse);

      expect(chat.displayName, equals("Custom Chat Name"));
    });
  });
}
