import 'package:flutter_test/flutter_test.dart';
import 'package:hia_sang_ma/services/socket_service.dart';
import 'package:hia_sang_ma/services/chat_socket_service.dart';
import 'package:hia_sang_ma/models/chat_model.dart';
import 'package:hia_sang_ma/models/user_model.dart';

void main() {
  group('Socket.IO Services Tests', () {
    late SocketService socketService;
    late ChatSocketService chatSocketService;

    setUp(() {
      socketService = SocketService();
      chatSocketService = ChatSocketService();
    });

    tearDown(() {
      socketService.dispose();
      chatSocketService.dispose();
    });

    test('SocketService should initialize correctly', () {
      expect(socketService.isConnected, false);
      expect(socketService.userId, null);
    });

    test('ChatSocketService should create optimistic message correctly', () {
      final user = UserModel(
        id: 1,
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'member',
      );

      final optimisticMessage = chatSocketService.createOptimisticMessage(
        content: 'Test message',
        messageType: MessageType.text,
        chatId: 1,
        currentUser: user,
      );

      expect(optimisticMessage.content, 'Test message');
      expect(optimisticMessage.messageType, MessageType.text);
      expect(optimisticMessage.messageStatus, MessageStatus.sending);
      expect(optimisticMessage.userId, 1);
      expect(optimisticMessage.chatId, 1);
      expect(optimisticMessage.user, user);
    });

    test('ChatMessage copyWith should work correctly', () {
      final user = UserModel(
        id: 1,
        email: '<EMAIL>',
        firstName: 'Test',
        lastName: 'User',
        role: 'member',
      );

      final originalMessage = ChatMessage(
        id: 1,
        chatId: 1,
        userId: 1,
        content: 'Original content',
        messageType: MessageType.text,
        messageStatus: MessageStatus.sending,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        user: user,
      );

      final updatedMessage = originalMessage.copyWith(
        messageStatus: MessageStatus.delivered,
        content: 'Updated content',
      );

      expect(updatedMessage.messageStatus, MessageStatus.delivered);
      expect(updatedMessage.content, 'Updated content');
      expect(updatedMessage.id, originalMessage.id);
      expect(updatedMessage.userId, originalMessage.userId);
    });

    test('MessageType.fromString should parse correctly', () {
      expect(MessageType.fromString('TEXT'), MessageType.text);
      expect(MessageType.fromString('text'), MessageType.text);
      expect(MessageType.fromString('IMAGE'), MessageType.image);
      expect(MessageType.fromString('FILE'), MessageType.file);
      expect(MessageType.fromString('STICKER'), MessageType.sticker);
      expect(MessageType.fromString('LINK'), MessageType.link);
      expect(MessageType.fromString('UNKNOWN'), MessageType.text); // default
    });

    test('MessageStatus.fromString should parse correctly', () {
      expect(MessageStatus.fromString('SENDING'), MessageStatus.sending);
      expect(MessageStatus.fromString('DELIVERED'), MessageStatus.delivered);
      expect(MessageStatus.fromString('READ'), MessageStatus.read);
      expect(MessageStatus.fromString('FAILED'), MessageStatus.failed);
      expect(MessageStatus.fromString('UNKNOWN'), MessageStatus.delivered); // default
    });
  });
}
