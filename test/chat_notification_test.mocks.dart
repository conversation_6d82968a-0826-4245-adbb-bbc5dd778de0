// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in hia_sang_ma/test/chat_notification_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:hia_sang_ma/models/chat_model.dart' as _i2;
import 'package:hia_sang_ma/models/user_model.dart' as _i7;
import 'package:hia_sang_ma/services/chat_service.dart' as _i3;
import 'package:hia_sang_ma/services/chat_socket_service.dart' as _i6;
import 'package:hia_sang_ma/services/unread_message_service.dart' as _i5;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeChatMessage_0 extends _i1.SmartFake implements _i2.ChatMessage {
  _FakeChatMessage_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [ChatService].
///
/// See the documentation for Mockito's code generation for more information.
class MockChatService extends _i1.Mock implements _i3.ChatService {
  MockChatService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<_i2.ChatModel>> getChats({
    int? id,
    _i2.ChatType? chatType,
    int? userId,
    int? organizationId,
    int? departmentId,
    int? taskId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getChats, [], {
              #id: id,
              #chatType: chatType,
              #userId: userId,
              #organizationId: organizationId,
              #departmentId: departmentId,
              #taskId: taskId,
            }),
            returnValue: _i4.Future<List<_i2.ChatModel>>.value(
              <_i2.ChatModel>[],
            ),
          )
          as _i4.Future<List<_i2.ChatModel>>);

  @override
  _i4.Future<_i2.ChatModel?> getChatById(int? chatId) =>
      (super.noSuchMethod(
            Invocation.method(#getChatById, [chatId]),
            returnValue: _i4.Future<_i2.ChatModel?>.value(),
          )
          as _i4.Future<_i2.ChatModel?>);

  @override
  _i4.Future<List<_i2.ChatMessage>> getChatMessages({
    required int? chatId,
    int? messageId,
    int? page = 1,
    int? limit = 20,
    int? before,
    int? after,
    bool? includeReadStatus = false,
    String? orderBy = 'created',
    String? orderDirection = 'DESC',
  }) =>
      (super.noSuchMethod(
            Invocation.method(#getChatMessages, [], {
              #chatId: chatId,
              #messageId: messageId,
              #page: page,
              #limit: limit,
              #before: before,
              #after: after,
              #includeReadStatus: includeReadStatus,
              #orderBy: orderBy,
              #orderDirection: orderDirection,
            }),
            returnValue: _i4.Future<List<_i2.ChatMessage>>.value(
              <_i2.ChatMessage>[],
            ),
          )
          as _i4.Future<List<_i2.ChatMessage>>);

  @override
  _i4.Future<int> getUnreadMessageCountForChat(int? chatId) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadMessageCountForChat, [chatId]),
            returnValue: _i4.Future<int>.value(0),
          )
          as _i4.Future<int>);

  @override
  _i4.Future<List<_i2.UnreadChatInfo>> getAllUnreadMessageCounts() =>
      (super.noSuchMethod(
            Invocation.method(#getAllUnreadMessageCounts, []),
            returnValue: _i4.Future<List<_i2.UnreadChatInfo>>.value(
              <_i2.UnreadChatInfo>[],
            ),
          )
          as _i4.Future<List<_i2.UnreadChatInfo>>);

  @override
  _i4.Future<int> getTotalUnreadMessageCount() =>
      (super.noSuchMethod(
            Invocation.method(#getTotalUnreadMessageCount, []),
            returnValue: _i4.Future<int>.value(0),
          )
          as _i4.Future<int>);

  @override
  _i4.Future<int> getUnreadMessageCount() =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadMessageCount, []),
            returnValue: _i4.Future<int>.value(0),
          )
          as _i4.Future<int>);

  @override
  _i4.Future<Map<String, dynamic>> markMessagesAsRead({
    int? messageId,
    int? chatId,
    int? beforeMessageId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#markMessagesAsRead, [], {
              #messageId: messageId,
              #chatId: chatId,
              #beforeMessageId: beforeMessageId,
            }),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);

  @override
  _i4.Future<Map<_i2.ChatType, int>> getUnreadCountsByType() =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCountsByType, []),
            returnValue: _i4.Future<Map<_i2.ChatType, int>>.value(
              <_i2.ChatType, int>{},
            ),
          )
          as _i4.Future<Map<_i2.ChatType, int>>);

  @override
  _i4.Future<List<_i2.ChatModel>> getChatsByType(_i2.ChatType? chatType) =>
      (super.noSuchMethod(
            Invocation.method(#getChatsByType, [chatType]),
            returnValue: _i4.Future<List<_i2.ChatModel>>.value(
              <_i2.ChatModel>[],
            ),
          )
          as _i4.Future<List<_i2.ChatModel>>);

  @override
  _i4.Future<Map<_i2.ChatType, List<_i2.ChatModel>>>
  getAllChatsGroupedByType() =>
      (super.noSuchMethod(
            Invocation.method(#getAllChatsGroupedByType, []),
            returnValue:
                _i4.Future<Map<_i2.ChatType, List<_i2.ChatModel>>>.value(
                  <_i2.ChatType, List<_i2.ChatModel>>{},
                ),
          )
          as _i4.Future<Map<_i2.ChatType, List<_i2.ChatModel>>>);
}

/// A class which mocks [UnreadMessageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockUnreadMessageService extends _i1.Mock
    implements _i5.UnreadMessageService {
  MockUnreadMessageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<Map<int, int>> get unreadCountsStream =>
      (super.noSuchMethod(
            Invocation.getter(#unreadCountsStream),
            returnValue: _i4.Stream<Map<int, int>>.empty(),
          )
          as _i4.Stream<Map<int, int>>);

  @override
  _i4.Stream<Map<_i2.ChatType, int>> get unreadCountsByTypeStream =>
      (super.noSuchMethod(
            Invocation.getter(#unreadCountsByTypeStream),
            returnValue: _i4.Stream<Map<_i2.ChatType, int>>.empty(),
          )
          as _i4.Stream<Map<_i2.ChatType, int>>);

  @override
  _i4.Stream<int> get totalUnreadCountStream =>
      (super.noSuchMethod(
            Invocation.getter(#totalUnreadCountStream),
            returnValue: _i4.Stream<int>.empty(),
          )
          as _i4.Stream<int>);

  @override
  _i4.Stream<_i5.UnreadCountUpdate> get unreadCountUpdateStream =>
      (super.noSuchMethod(
            Invocation.getter(#unreadCountUpdateStream),
            returnValue: _i4.Stream<_i5.UnreadCountUpdate>.empty(),
          )
          as _i4.Stream<_i5.UnreadCountUpdate>);

  @override
  _i4.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<int> getUnreadCountForChat(int? chatId) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCountForChat, [chatId]),
            returnValue: _i4.Future<int>.value(0),
          )
          as _i4.Future<int>);

  @override
  int getUnreadCountForType(_i2.ChatType? chatType) =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCountForType, [chatType]),
            returnValue: 0,
          )
          as int);

  @override
  int getTotalUnreadCount() =>
      (super.noSuchMethod(
            Invocation.method(#getTotalUnreadCount, []),
            returnValue: 0,
          )
          as int);

  @override
  Map<int, int> getAllUnreadCounts() =>
      (super.noSuchMethod(
            Invocation.method(#getAllUnreadCounts, []),
            returnValue: <int, int>{},
          )
          as Map<int, int>);

  @override
  Map<_i2.ChatType, int> getUnreadCountsByType() =>
      (super.noSuchMethod(
            Invocation.method(#getUnreadCountsByType, []),
            returnValue: <_i2.ChatType, int>{},
          )
          as Map<_i2.ChatType, int>);

  @override
  _i4.Future<void> refreshAllUnreadCounts() =>
      (super.noSuchMethod(
            Invocation.method(#refreshAllUnreadCounts, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void updateUnreadCountForChat(int? chatId, int? newCount) =>
      super.noSuchMethod(
        Invocation.method(#updateUnreadCountForChat, [chatId, newCount]),
        returnValueForMissingStub: null,
      );

  @override
  void updateUnreadCountForChatWithType(
    int? chatId,
    int? newCount,
    _i2.ChatType? chatType,
  ) => super.noSuchMethod(
    Invocation.method(#updateUnreadCountForChatWithType, [
      chatId,
      newCount,
      chatType,
    ]),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<void> markMessagesAsRead({
    int? messageId,
    int? chatId,
    int? beforeMessageId,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#markMessagesAsRead, [], {
              #messageId: messageId,
              #chatId: chatId,
              #beforeMessageId: beforeMessageId,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> clearCacheAndRefresh() =>
      (super.noSuchMethod(
            Invocation.method(#clearCacheAndRefresh, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [ChatSocketService].
///
/// See the documentation for Mockito's code generation for more information.
class MockChatSocketService extends _i1.Mock implements _i6.ChatSocketService {
  MockChatSocketService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i2.ChatMessage> get newMessageStream =>
      (super.noSuchMethod(
            Invocation.getter(#newMessageStream),
            returnValue: _i4.Stream<_i2.ChatMessage>.empty(),
          )
          as _i4.Stream<_i2.ChatMessage>);

  @override
  _i4.Stream<Map<String, dynamic>> get chatNotificationStream =>
      (super.noSuchMethod(
            Invocation.getter(#chatNotificationStream),
            returnValue: _i4.Stream<Map<String, dynamic>>.empty(),
          )
          as _i4.Stream<Map<String, dynamic>>);

  @override
  _i4.Stream<Map<String, dynamic>> get messagesReadStream =>
      (super.noSuchMethod(
            Invocation.getter(#messagesReadStream),
            returnValue: _i4.Stream<Map<String, dynamic>>.empty(),
          )
          as _i4.Stream<Map<String, dynamic>>);

  @override
  _i4.Stream<Map<String, dynamic>> get userJoinedStream =>
      (super.noSuchMethod(
            Invocation.getter(#userJoinedStream),
            returnValue: _i4.Stream<Map<String, dynamic>>.empty(),
          )
          as _i4.Stream<Map<String, dynamic>>);

  @override
  _i4.Stream<Map<String, dynamic>> get userLeftStream =>
      (super.noSuchMethod(
            Invocation.getter(#userLeftStream),
            returnValue: _i4.Stream<Map<String, dynamic>>.empty(),
          )
          as _i4.Stream<Map<String, dynamic>>);

  @override
  _i4.Stream<Map<String, dynamic>> get unreadCountStream =>
      (super.noSuchMethod(
            Invocation.getter(#unreadCountStream),
            returnValue: _i4.Stream<Map<String, dynamic>>.empty(),
          )
          as _i4.Stream<Map<String, dynamic>>);

  @override
  void setupChatEventListeners() => super.noSuchMethod(
    Invocation.method(#setupChatEventListeners, []),
    returnValueForMissingStub: null,
  );

  @override
  void sendMessage({
    required String? chatId,
    required String? chatType,
    required int? userId,
    required String? content,
    required String? messageType,
    String? optimisticId,
    dynamic Function(dynamic)? onAck,
  }) => super.noSuchMethod(
    Invocation.method(#sendMessage, [], {
      #chatId: chatId,
      #chatType: chatType,
      #userId: userId,
      #content: content,
      #messageType: messageType,
      #optimisticId: optimisticId,
      #onAck: onAck,
    }),
    returnValueForMissingStub: null,
  );

  @override
  void joinChat(String? chatId) => super.noSuchMethod(
    Invocation.method(#joinChat, [chatId]),
    returnValueForMissingStub: null,
  );

  @override
  void leaveChat(String? chatId) => super.noSuchMethod(
    Invocation.method(#leaveChat, [chatId]),
    returnValueForMissingStub: null,
  );

  @override
  void markMessagesAsRead({required String? chatId, required int? userId}) =>
      super.noSuchMethod(
        Invocation.method(#markMessagesAsRead, [], {
          #chatId: chatId,
          #userId: userId,
        }),
        returnValueForMissingStub: null,
      );

  @override
  void deleteMessage({required String? chatId, required String? messageId}) =>
      super.noSuchMethod(
        Invocation.method(#deleteMessage, [], {
          #chatId: chatId,
          #messageId: messageId,
        }),
        returnValueForMissingStub: null,
      );

  @override
  _i2.ChatMessage createOptimisticMessage({
    required String? content,
    required _i2.MessageType? messageType,
    required int? chatId,
    required _i7.UserModel? currentUser,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createOptimisticMessage, [], {
              #content: content,
              #messageType: messageType,
              #chatId: chatId,
              #currentUser: currentUser,
            }),
            returnValue: _FakeChatMessage_0(
              this,
              Invocation.method(#createOptimisticMessage, [], {
                #content: content,
                #messageType: messageType,
                #chatId: chatId,
                #currentUser: currentUser,
              }),
            ),
          )
          as _i2.ChatMessage);

  @override
  void removeAllListeners() => super.noSuchMethod(
    Invocation.method(#removeAllListeners, []),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}
