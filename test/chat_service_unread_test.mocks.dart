// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in hia_sang_ma/test/chat_service_unread_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:hia_sang_ma/models/login_response.dart' as _i2;
import 'package:hia_sang_ma/models/user_model.dart' as _i3;
import 'package:hia_sang_ma/services/auth_service.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeLoginResponse_0 extends _i1.SmartFake implements _i2.LoginResponse {
  _FakeLoginResponse_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUserModel_1 extends _i1.SmartFake implements _i3.UserModel {
  _FakeUserModel_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthService extends _i1.Mock implements _i4.AuthService {
  MockAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.LoginResponse> login(
    String? email,
    String? password, {
    bool? rememberMe = false,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #login,
              [email, password],
              {#rememberMe: rememberMe},
            ),
            returnValue: _i5.Future<_i2.LoginResponse>.value(
              _FakeLoginResponse_0(
                this,
                Invocation.method(
                  #login,
                  [email, password],
                  {#rememberMe: rememberMe},
                ),
              ),
            ),
          )
          as _i5.Future<_i2.LoginResponse>);

  @override
  _i5.Future<String?> getToken() =>
      (super.noSuchMethod(
            Invocation.method(#getToken, []),
            returnValue: _i5.Future<String?>.value(),
          )
          as _i5.Future<String?>);

  @override
  _i5.Future<_i3.UserModel?> getCurrentUser() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentUser, []),
            returnValue: _i5.Future<_i3.UserModel?>.value(),
          )
          as _i5.Future<_i3.UserModel?>);

  @override
  _i5.Future<bool> isLoggedIn() =>
      (super.noSuchMethod(
            Invocation.method(#isLoggedIn, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<void> logout() =>
      (super.noSuchMethod(
            Invocation.method(#logout, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<void> logoutAndRedirect() =>
      (super.noSuchMethod(
            Invocation.method(#logoutAndRedirect, []),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);

  @override
  _i5.Future<_i3.UserModel> getCurrentUserProfile() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentUserProfile, []),
            returnValue: _i5.Future<_i3.UserModel>.value(
              _FakeUserModel_1(
                this,
                Invocation.method(#getCurrentUserProfile, []),
              ),
            ),
          )
          as _i5.Future<_i3.UserModel>);

  @override
  _i5.Future<_i3.UserModel> updateUserProfile({
    String? firstName,
    String? lastName,
    String? phone,
    String? imageUrl,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserProfile, [], {
              #firstName: firstName,
              #lastName: lastName,
              #phone: phone,
              #imageUrl: imageUrl,
            }),
            returnValue: _i5.Future<_i3.UserModel>.value(
              _FakeUserModel_1(
                this,
                Invocation.method(#updateUserProfile, [], {
                  #firstName: firstName,
                  #lastName: lastName,
                  #phone: phone,
                  #imageUrl: imageUrl,
                }),
              ),
            ),
          )
          as _i5.Future<_i3.UserModel>);

  @override
  _i5.Future<void> changePassword({
    required String? currentPassword,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#changePassword, [], {
              #currentPassword: currentPassword,
              #newPassword: newPassword,
            }),
            returnValue: _i5.Future<void>.value(),
            returnValueForMissingStub: _i5.Future<void>.value(),
          )
          as _i5.Future<void>);
}
