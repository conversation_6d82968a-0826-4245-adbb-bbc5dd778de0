# Socket.IO Real-time Chat Implementation

This document describes the Socket.IO implementation for real-time chat messaging in the Flutter project.

## Overview

The implementation provides real-time bidirectional communication for chat messages using Socket.IO, including:
- Real-time message sending and receiving
- Optimistic UI updates
- Connection management and error handling
- Message status indicators (sending, delivered, read, failed)
- Automatic reconnection logic

## Architecture

### Core Components

1. **SocketService** (`lib/services/socket_service.dart`)
   - Core Socket.IO connection management
   - Event emission and listening
   - Connection lifecycle management
   - Error handling and reconnection logic

2. **ChatSocketService** (`lib/services/chat_socket_service.dart`)
   - Chat-specific Socket.IO events
   - Message parsing and handling
   - Optimistic message creation
   - Stream-based event broadcasting

3. **SocketConfig** (`lib/config/socket_config.dart`)
   - Configuration settings for Socket.IO
   - Server URL and connection parameters
   - Timeout and retry settings

4. **Enhanced Chat Models** (`lib/models/chat_model.dart`)
   - Added `sending` status to MessageStatus enum
   - Added optimistic message support
   - Added copyWith method for message updates

### Integration Points

- **ChatDetailScreen** (`lib/screens/chat_detail_screen.dart`)
  - Integrated Socket.IO connection management
  - Real-time message sending and receiving
  - UI updates for connection status
  - Message retry functionality

## Features Implemented

### ✅ Real-time Message Sending
- Optimistic UI updates for immediate feedback
- Socket.IO message emission
- Timeout handling for failed messages
- Message retry functionality

### ✅ Real-time Message Receiving
- Live message updates from other users
- Duplicate message prevention
- Automatic scroll to new messages
- Message confirmation handling

### ✅ Connection Management
- Automatic connection on screen load
- Proper cleanup on screen disposal
- Connection status indicators
- Join/leave chat room functionality

### ✅ Error Handling & Reconnection
- Automatic reconnection with exponential backoff
- Connection error notifications
- Failed message indicators with retry option
- Network state monitoring

### ✅ Message Status System
- **Sending**: Shows loading indicator
- **Delivered**: Shows single check mark
- **Read**: Shows double check mark (blue)
- **Failed**: Shows error icon with retry option

## Socket Events

### Client → Server Events
- `send_message`: Send a new message
- `join_chat`: Join a chat room
- `leave_chat`: Leave a chat room
- `messages_read`: Mark messages as read

### Server → Client Events
- `new_message`: Receive new message
- `chat_notification`: Chat updates/notifications
- `messages_read`: Message read status updates
- `receive_join`: User joined chat
- `receive_leave`: User left chat
- `unread_count_updated`: Unread count changes

## Configuration

### Server URL Configuration
Update the server URL in `lib/config/socket_config.dart`:

```dart
static const String serverUrl = 'ws://your-server-url:8081';
```

### Connection Settings
- Connection timeout: 10 seconds
- Reconnection delay: 2 seconds (exponential backoff)
- Max reconnection attempts: 5
- Message timeout: 10 seconds

## Usage

### Basic Usage
The Socket.IO functionality is automatically integrated into the ChatDetailScreen. When users open a chat:

1. Socket connection is established
2. User joins the chat room
3. Real-time messaging is enabled
4. Connection status is displayed

### Message Flow
1. User types and sends a message
2. Optimistic message appears immediately
3. Message is sent via Socket.IO
4. Server confirms message delivery
5. Optimistic message is replaced with confirmed message

### Error Handling
- Connection errors show notifications
- Failed messages display retry button
- Automatic reconnection attempts
- Graceful degradation to read-only mode

## Testing

Run the Socket.IO tests:
```bash
flutter test test/socket_io_test.dart
```

Tests cover:
- Service initialization
- Message creation and updates
- Enum parsing
- Model functionality

## Dependencies Added

```yaml
dependencies:
  socket_io_client: ^2.0.3+1
  flutter_bloc: ^8.1.3
  json_annotation: ^4.8.1

dev_dependencies:
  json_serializable: ^6.7.1
  build_runner: ^2.4.7
```

## File Structure

```
lib/
├── config/
│   └── socket_config.dart          # Socket.IO configuration
├── services/
│   ├── socket_service.dart         # Core Socket.IO service
│   └── chat_socket_service.dart    # Chat-specific Socket.IO service
├── models/
│   └── chat_model.dart            # Enhanced with Socket.IO support
└── screens/
    └── chat_detail_screen.dart    # Integrated Socket.IO functionality

test/
└── socket_io_test.dart            # Socket.IO tests
```

## Next Steps

To complete the implementation:

1. **Server Setup**: Ensure your Socket.IO server is running and configured
2. **URL Configuration**: Update the server URL in SocketConfig
3. **Testing**: Test with real server connection
4. **Production**: Configure environment-specific URLs
5. **Monitoring**: Add logging and analytics

## Troubleshooting

### Common Issues

1. **Connection Failed**
   - Check server URL and port
   - Verify server is running
   - Check network connectivity

2. **Messages Not Sending**
   - Verify Socket.IO connection status
   - Check server event handlers
   - Review console logs

3. **Duplicate Messages**
   - Ensure proper message ID handling
   - Check optimistic message replacement logic

### Debug Mode
Enable logging in SocketConfig:
```dart
static const bool enableLogging = true;
```

This will show detailed Socket.IO events in the console.
