import 'package:flutter/material.dart';
import 'package:hia_sang_ma/services/socket_service.dart';
import 'package:hia_sang_ma/services/chat_socket_service.dart';
import 'package:hia_sang_ma/services/auth_service.dart';

/// Debug screen to test socket connection and events
class SocketDebugScreen extends StatefulWidget {
  const SocketDebugScreen({super.key});

  @override
  State<SocketDebugScreen> createState() => _SocketDebugScreenState();
}

class _SocketDebugScreenState extends State<SocketDebugScreen> {
  final SocketService _socketService = SocketService();
  final ChatSocketService _chatSocketService = ChatSocketService();
  final AuthService _authService = AuthService();

  bool _isConnected = false;
  List<String> _events = [];
  String? _userId;

  @override
  void initState() {
    super.initState();
    _initializeSocket();
  }

  Future<void> _initializeSocket() async {
    try {
      final user = await _authService.getCurrentUser();
      if (user != null) {
        _userId = user.id.toString();
        await _socketService.connect(_userId!);
        _chatSocketService.setupChatEventListeners();

        // Listen to connection status
        _socketService.connectionStream.listen((connected) {
          setState(() {
            _isConnected = connected;
            _events.add(
              'Connection: ${connected ? 'Connected' : 'Disconnected'}',
            );
          });
        });

        // Listen to chat notifications
        _chatSocketService.chatNotificationStream.listen((data) {
          setState(() {
            _events.add('Chat Notification: $data');
          });
        });

        // Listen to new messages
        _chatSocketService.newMessageStream.listen((message) {
          setState(() {
            _events.add('New Message: ${message.content}');
          });
        });

        setState(() {
          _isConnected = _socketService.isConnected;
        });
      }
    } catch (e) {
      setState(() {
        _events.add('Error: $e');
      });
    }
  }

  void _sendTestNotification() {
    // Simulate sending a test notification
    _socketService.emit('test_chat_notification', {
      'type': 'new_message',
      'chatId': 123,
      'chatType': 'PRIVATE',
      'message': 'Test message from debug screen',
    });

    setState(() {
      _events.add('Sent test notification');
    });
  }

  void _simulateUnreadCountUpdate() {
    // Simulate unread count update for testing bottom nav badge
    _socketService.emit('unread_count_updated', {
      'chatId': 123,
      'unreadCount': 5,
      'chatType': 'PRIVATE',
    });

    setState(() {
      _events.add('Simulated unread count update: 5');
    });
  }

  void _clearEvents() {
    setState(() {
      _events.clear();
    });
  }

  @override
  void dispose() {
    _chatSocketService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Socket Debug'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(icon: const Icon(Icons.clear), onPressed: _clearEvents),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Connection Status
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Connection Status',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          _isConnected ? Icons.wifi : Icons.wifi_off,
                          color: _isConnected ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _isConnected ? 'Connected' : 'Disconnected',
                          style: TextStyle(
                            color: _isConnected ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    if (_userId != null) ...[
                      const SizedBox(height: 8),
                      Text('User ID: $_userId'),
                    ],
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Test Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isConnected ? _sendTestNotification : null,
                    child: const Text('Send Test Notification'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isConnected ? _simulateUnreadCountUpdate : null,
                    child: const Text('Test Unread Count'),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Events List
            Text(
              'Events (${_events.length})',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),

            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _events.isEmpty
                    ? const Center(child: Text('No events yet'))
                    : ListView.builder(
                        itemCount: _events.length,
                        itemBuilder: (context, index) {
                          final event =
                              _events[_events.length -
                                  1 -
                                  index]; // Reverse order
                          return ListTile(
                            dense: true,
                            leading: Text(
                              '${_events.length - index}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.grey,
                              ),
                            ),
                            title: Text(
                              event,
                              style: const TextStyle(fontSize: 12),
                            ),
                            subtitle: Text(
                              DateTime.now().toString().substring(11, 19),
                              style: const TextStyle(fontSize: 10),
                            ),
                          );
                        },
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
