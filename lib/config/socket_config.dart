/// Socket.IO configuration settings for the chat application
class SocketConfig {
  /// Base server URL for Socket.IO connection
  /// TODO: Replace with your actual server URL
  static const String serverUrl =
      'wss://hia-sang-ma-socket-production.up.railway.app';

  /// Connection timeout duration
  static const Duration connectionTimeout = Duration(seconds: 20);

  /// Delay between reconnection attempts
  static const Duration reconnectionDelay = Duration(seconds: 3);

  /// Maximum number of reconnection attempts
  static const int maxReconnectAttempts = 10;

  /// Heartbeat interval for keeping connection alive
  static const Duration heartbeatInterval = Duration(seconds: 25);

  /// Timeout for heartbeat response
  static const Duration heartbeatTimeout = Duration(seconds: 10);

  /// Enable auto-connect on initialization
  static const bool autoConnect = true;

  /// Enable logging for debugging
  static const bool enableLogging = true;

  /// Supported transport methods - adding polling as fallback
  static const List<String> transports = ['websocket', 'polling'];

  /// Socket.IO namespace for chat functionality
  /// Using default namespace - change to '/chat' if your server uses a specific namespace
  static const String chatNamespace = '';

  /// Default query parameters for connection
  static Map<String, String> getDefaultQuery(String userId) {
    return {'userId': userId, 'platform': 'flutter', 'version': '1.0.0'};
  }

  /// Get full socket URL with namespace
  static String getSocketUrl({String? namespace}) {
    final ns = namespace ?? chatNamespace;
    // If namespace is empty, just return the server URL
    return ns.isEmpty ? serverUrl : '$serverUrl$ns';
  }

  /// Alternative configurations for different server setups
  static String getSocketUrlWithPath(String path) {
    return '$serverUrl$path';
  }

  /// Environment-specific configurations
  static String get environmentServerUrl {
    // TODO: Implement environment-specific URLs
    // For now, return the default URL
    return serverUrl;
  }
}
