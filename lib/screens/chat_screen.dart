import 'dart:async';
import 'package:flutter/material.dart';
import 'package:hia_sang_ma/constants/app_colors.dart';
import 'package:hia_sang_ma/constants/app_strings.dart';
import 'package:hia_sang_ma/models/chat_model.dart';
import 'package:hia_sang_ma/services/chat_service.dart';
import 'package:hia_sang_ma/services/unread_message_service.dart';
import 'package:hia_sang_ma/services/chat_socket_service.dart';
import 'package:hia_sang_ma/services/auth_service.dart';
import 'package:hia_sang_ma/services/socket_service.dart';
import 'package:hia_sang_ma/screens/chat_detail_screen.dart';

class ChatScreen extends StatefulWidget {
  const ChatScreen({super.key});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> with TickerProviderStateMixin {
  final ChatService _chatService = ChatService();
  final UnreadMessageService _unreadMessageService = UnreadMessageService();
  final ChatSocketService _chatSocketService = ChatSocketService();
  final AuthService _authService = AuthService();
  final SocketService _socketService = SocketService();

  late TabController _tabController;
  bool _isLoading = true;
  bool _isRefreshingFromNotification = false;
  String? _errorMessage;
  Map<ChatType, List<ChatModel>> _groupedChats = {};
  ChatType _selectedChatType = ChatType.private;
  Map<ChatType, int> _unreadCountsByType = {};
  StreamSubscription<Map<ChatType, int>>? _unreadCountsSubscription;
  StreamSubscription<Map<String, dynamic>>? _chatNotificationSubscription;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: ChatType.values.length, vsync: this);
    _tabController.addListener(_onTabChanged);
    _initializeUnreadCounts();
    _initializeSocketConnection();
    _loadChats();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _unreadCountsSubscription?.cancel();
    _chatNotificationSubscription?.cancel();
    _chatSocketService.dispose();
    // Disconnect socket when leaving chat screen
    _socketService.disconnect();
    super.dispose();
  }

  /// Initialize socket connection and set up listeners
  Future<void> _initializeSocketConnection() async {
    try {
      // Get current user for socket connection
      final user = await _authService.getCurrentUser();
      if (user != null) {
        // Connect to socket with user ID
        await _socketService.connect(user.id.toString());
        print('🔌 Socket connected for user: ${user.id}');

        // Set up chat socket event listeners
        _setupSocketListeners();
      } else {
        print('❌ No user found for socket connection');
      }
    } catch (e) {
      print('❌ Error initializing socket connection: $e');
    }
  }

  /// Set up socket listeners for real-time updates
  void _setupSocketListeners() {
    // Set up chat socket event listeners
    _chatSocketService.setupChatEventListeners();

    // Listen for chat notifications
    _chatNotificationSubscription = _chatSocketService.chatNotificationStream
        .listen(
          _handleChatNotification,
          onError: (error) {
            print('Error in chat notification stream: $error');
          },
        );
  }

  /// Initialize unread counts and set up listeners
  void _initializeUnreadCounts() {
    // Initialize unread counts for all chat types
    for (final chatType in ChatType.values) {
      _unreadCountsByType[chatType] = 0;
    }

    // Initialize the unread message service
    _unreadMessageService.initialize();

    // Listen to unread counts by type updates
    _unreadCountsSubscription = _unreadMessageService.unreadCountsByTypeStream
        .listen((counts) {
          if (mounted) {
            setState(() {
              _unreadCountsByType = counts;
            });
          }
        });

    // Load initial unread counts
    _loadUnreadCounts();
  }

  /// Load unread counts from the service
  Future<void> _loadUnreadCounts() async {
    try {
      await _unreadMessageService.refreshAllUnreadCounts();
      if (mounted) {
        setState(() {
          _unreadCountsByType = _unreadMessageService.getUnreadCountsByType();
        });
      }
    } catch (e) {
      print('Error loading unread counts: $e');
    }
  }

  /// Handle chat notification events from WebSocket
  void _handleChatNotification(Map<String, dynamic> notificationData) {
    print('📢 Chat notification received in ChatScreen: $notificationData');

    try {
      // Extract notification type and relevant data
      final notificationType = notificationData['type']?.toString();
      final chatId = notificationData['chatId'];
      final chatType = notificationData['chatType']?.toString();

      print(
        '📢 Notification type: $notificationType, chatId: $chatId, chatType: $chatType',
      );

      // Trigger refresh based on notification type
      switch (notificationType) {
        case 'new_message':
        case 'message_updated':
        case 'message_deleted':
        case 'chat_updated':
        case 'user_joined':
        case 'user_left':
          _refreshDataFromNotification();
          break;
        default:
          print('📢 Unknown notification type: $notificationType');
          // Still refresh for unknown types to be safe
          _refreshDataFromNotification();
      }
    } catch (e) {
      print('Error handling chat notification: $e');
      // Still try to refresh on error to ensure data consistency
      _refreshDataFromNotification();
    }
  }

  /// Refresh chat data when notification is received
  Future<void> _refreshDataFromNotification() async {
    if (!mounted) return;

    try {
      // Set loading state for notification refresh
      setState(() {
        _isRefreshingFromNotification = true;
      });

      // Show brief loading indicator
      // _showNotificationRefreshIndicator();

      // Refresh both chat data and unread counts in parallel
      await Future.wait([_refreshChatsData(), _loadUnreadCounts()]);

      print('📢 Successfully refreshed data from notification');
    } catch (e) {
      print('Error refreshing data from notification: $e');
      // Show error feedback to user
      _showRefreshErrorFeedback();
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshingFromNotification = false;
        });
      }
    }
  }

  /// Refresh chat data without changing loading state
  Future<void> _refreshChatsData() async {
    try {
      final groupedChats = await _chatService.getAllChatsGroupedByType();
      if (mounted) {
        setState(() {
          _groupedChats = groupedChats;
        });
      }
    } catch (e) {
      print('Error refreshing chats data: $e');
      rethrow;
    }
  }

  /// Show brief loading indicator for notification refresh
  // void _showNotificationRefreshIndicator() {
  //   if (mounted) {
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       SnackBar(
  //         content: Row(
  //           mainAxisSize: MainAxisSize.min,
  //           children: [
  //             SizedBox(
  //               width: 16,
  //               height: 16,
  //               child: CircularProgressIndicator(
  //                 strokeWidth: 2,
  //                 valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
  //               ),
  //             ),
  //             const SizedBox(width: 12),
  //             const Text('กำลังอัปเดตข้อมูลแชท...'),
  //           ],
  //         ),
  //         duration: const Duration(seconds: 1),
  //         backgroundColor: AppColors.primary,
  //       ),
  //     );
  //   }
  // }

  /// Show error feedback when refresh fails
  void _showRefreshErrorFeedback() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('ไม่สามารถอัปเดตข้อมูลแชทได้ กรุณาลองใหม่'),
          duration: Duration(seconds: 3),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) {
      setState(() {
        _selectedChatType = ChatType.values[_tabController.index];
      });
    }
  }

  Future<void> _loadChats() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Load chats and unread counts in parallel
      final futures = await Future.wait([
        _chatService.getAllChatsGroupedByType(),
        _loadUnreadCounts(),
      ]);

      final groupedChats = futures[0] as Map<ChatType, List<ChatModel>>;

      setState(() {
        _groupedChats = groupedChats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  String _cleanHtmlForPreview(String htmlContent) {
    // Remove HTML tags and decode HTML entities for preview
    String cleanText = htmlContent
        .replaceAll(RegExp(r'<[^>]*>'), '') // Remove HTML tags
        .replaceAll('&nbsp;', ' ') // Replace &nbsp; with space
        .replaceAll('&amp;', '&') // Replace &amp; with &
        .replaceAll('&lt;', '<') // Replace &lt; with <
        .replaceAll('&gt;', '>') // Replace &gt; with >
        .replaceAll('&quot;', '"') // Replace &quot; with "
        .trim();

    return cleanText.isEmpty ? 'ไม่มีข้อความ' : cleanText;
  }

  Future<Map<String, dynamic>> _getChatDisplayData(ChatModel chat) async {
    if (chat.chatType == ChatType.private) {
      // For private chats, get the other participant's information
      final otherUser = await chat.getOtherParticipantUser();
      if (otherUser != null) {
        final firstName = otherUser.firstName.trim();
        final lastName = otherUser.lastName.trim();

        String name;
        if (firstName.isNotEmpty && lastName.isNotEmpty) {
          name = '$firstName $lastName';
        } else if (firstName.isNotEmpty) {
          name = firstName;
        } else if (lastName.isNotEmpty) {
          name = lastName;
        } else {
          name = 'แชทส่วนตัว #${chat.id}';
        }

        return {'name': name, 'imageUrl': otherUser.imageUrl};
      }
    }

    // For non-private chats or when other user is not found, use default logic
    String? userImageUrl;
    if (chat.chatType == ChatType.private) {
      // Prioritize direct user object first
      if (chat.user != null) {
        userImageUrl = chat.user!.imageUrl;
      } else if (chat.participants != null && chat.participants!.isNotEmpty) {
        userImageUrl = chat.participants!.first.user.imageUrl;
      }
    }

    return {'name': chat.displayName, 'imageUrl': userImageUrl};
  }

  /// Build a tab with unread message badge
  Widget _buildTabWithBadge(ChatType chatType) {
    final unreadCount = _unreadCountsByType[chatType] ?? 0;

    return Tab(
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Text(chatType.displayName),
          if (unreadCount > 0)
            Positioned(
              right: -8,
              top: -8,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(10),
                ),
                constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
                child: Text(
                  unreadCount > 99 ? '99+' : unreadCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        title: Text(AppStrings.chatTitle),
        elevation: 0,
        actions: [
          // Show notification refresh indicator
          if (_isRefreshingFromNotification)
            Container(
              margin: const EdgeInsets.only(right: 8),
              child: Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
            ),
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadChats),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              // Search functionality
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: false,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: ChatType.values
              .map((chatType) => _buildTabWithBadge(chatType))
              .toList(),
        ),
      ),
      body: _buildChatList(),
      floatingActionButton: FloatingActionButton(
        heroTag: "chat_fab",
        onPressed: () {
          // Start new chat
        },
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.chat, color: Colors.white),
      ),
    );
  }

  Widget _buildChatList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'เกิดข้อผิดพลาด',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[500]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(onPressed: _loadChats, child: const Text('ลองใหม่')),
          ],
        ),
      );
    }

    final chats = _groupedChats[_selectedChatType] ?? [];

    if (chats.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'ไม่มี${_selectedChatType.displayName}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'เริ่มแชทใหม่เพื่อเริ่มการสนทนา',
              style: TextStyle(color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadChats,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        itemCount: chats.length,
        itemBuilder: (context, index) {
          final chat = chats[index];
          return Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 6.0,
            ),
            child: _buildChatItem(chat: chat),
          );
        },
      ),
    );
  }

  Widget _buildChatItem({required ChatModel chat}) {
    final lastMessage = _cleanHtmlForPreview(chat.lastMessage?.content ?? '');
    final time =
        chat.lastMessage?.timeDisplay ??
        chat.updatedAt.toString().substring(11, 16);

    // Use real-time unread count from the service instead of the model
    return StreamBuilder<Map<int, int>>(
      stream: _unreadMessageService.unreadCountsStream,
      initialData: _unreadMessageService.getAllUnreadCounts(),
      builder: (context, snapshot) {
        final unreadCounts = snapshot.data ?? {};
        final unreadCount = unreadCounts[chat.id] ?? chat.unreadCount;

        return _buildChatItemContent(
          chat: chat,
          lastMessage: lastMessage,
          time: time,
          unreadCount: unreadCount,
        );
      },
    );
  }

  Widget _buildChatItemContent({
    required ChatModel chat,
    required String lastMessage,
    required String time,
    required int unreadCount,
  }) {
    return FutureBuilder<Map<String, dynamic>>(
      future: _getChatDisplayData(chat),
      builder: (context, snapshot) {
        final displayData =
            snapshot.data ?? {'name': chat.displayName, 'imageUrl': null};
        final name = displayData['name'] as String;
        final userImageUrl = displayData['imageUrl'] as String?;

        return GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ChatDetailScreen(chat: chat),
              ),
            );
          },
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Avatar
                CircleAvatar(
                  radius: 24,
                  backgroundColor: AppColors.primary,
                  backgroundImage:
                      userImageUrl != null && userImageUrl.isNotEmpty
                      ? NetworkImage(userImageUrl)
                      : null,
                  child: userImageUrl == null || userImageUrl.isEmpty
                      ? Text(
                          name.isNotEmpty ? name[0] : '?',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        )
                      : null,
                ),
                const SizedBox(width: 12),

                // Chat Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              name,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            time,
                            style: TextStyle(
                              color: Colors.grey[500],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              lastMessage,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 14,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (unreadCount > 0) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.red,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                unreadCount > 9 ? '9+' : unreadCount.toString(),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
