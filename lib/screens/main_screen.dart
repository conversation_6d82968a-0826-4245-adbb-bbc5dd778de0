import 'dart:async';
import 'package:flutter/material.dart';
import 'package:hia_sang_ma/constants/app_colors.dart';
import 'package:hia_sang_ma/constants/app_strings.dart';
import 'package:hia_sang_ma/screens/board_screen.dart';
import 'package:hia_sang_ma/screens/chat_screen.dart';
import 'package:hia_sang_ma/screens/notification_screen.dart';
import 'package:hia_sang_ma/screens/settings_screen.dart';
import 'package:hia_sang_ma/services/unread_message_service.dart';
import 'package:hia_sang_ma/widgets/notification_badge.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _selectedIndex = 0;
  final UnreadMessageService _unreadMessageService = UnreadMessageService();
  int _totalUnreadCount = 0;
  StreamSubscription<int>? _unreadCountSubscription;

  final List<Widget> _screens = const [
    BoardScreen(),
    ChatScreen(),
    NotificationScreen(),
    SettingsScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _initializeUnreadCounts();
  }

  @override
  void dispose() {
    _unreadCountSubscription?.cancel();
    super.dispose();
  }

  /// Initialize unread message service and set up listeners
  void _initializeUnreadCounts() {
    try {
      // Initialize the service
      _unreadMessageService.initialize();

      // Get initial count
      _totalUnreadCount = _unreadMessageService.getTotalUnreadCount();

      // Listen for real-time updates
      _unreadCountSubscription = _unreadMessageService.totalUnreadCountStream
          .listen(
            (count) {
              if (mounted) {
                setState(() {
                  _totalUnreadCount = count;
                });
              }
            },
            onError: (error) {
              print('Error in unread count stream: $error');
              // Reset count on error to prevent stale data
              if (mounted) {
                setState(() {
                  _totalUnreadCount = 0;
                });
              }
            },
          );
    } catch (e) {
      print('Error initializing unread counts: $e');
      // Ensure count is reset on initialization error
      _totalUnreadCount = 0;
    }
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: _selectedIndex, children: _screens),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.2),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: _selectedIndex,
          onTap: _onItemTapped,
          backgroundColor: Colors.white,
          selectedItemColor: AppColors.primary,
          unselectedItemColor: Colors.grey[600],
          selectedFontSize: 12,
          unselectedFontSize: 12,
          elevation: 0,
          items: [
            BottomNavigationBarItem(
              icon: _buildNavIcon(Icons.dashboard_outlined, Icons.dashboard, 0),
              label: AppStrings.board,
            ),
            BottomNavigationBarItem(
              icon: _buildChatNavIcon(),
              label: AppStrings.chat,
            ),
            BottomNavigationBarItem(
              icon: _buildNavIcon(
                Icons.notifications_outlined,
                Icons.notifications,
                2,
              ),
              label: AppStrings.notification,
            ),
            BottomNavigationBarItem(
              icon: _buildNavIcon(Icons.settings_outlined, Icons.settings, 3),
              label: AppStrings.settings,
            ),
          ],
        ),
      ),
    );
  }

  /// Build chat navigation icon with notification badge
  Widget _buildChatNavIcon() {
    final isSelected = _selectedIndex == 1; // Chat is at index 1
    final icon = Icon(
      isSelected ? Icons.chat_bubble : Icons.chat_bubble_outline,
      size: 24,
    );

    return BottomNavNotificationBadge(
      icon: icon,
      count: _totalUnreadCount,
      isSelected: isSelected,
    );
  }

  Widget _buildNavIcon(IconData outlined, IconData filled, int index) {
    final isSelected = _selectedIndex == index;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Icon(isSelected ? filled : outlined, size: 24),
    );
  }
}
