import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:mime/mime.dart';
import 'dart:io';

class FileMessageWidget extends StatefulWidget {
  final String fileUrl;
  final String fileName;
  final bool isMyMessage;
  final int? fileSize;

  const FileMessageWidget({
    super.key,
    required this.fileUrl,
    required this.fileName,
    required this.isMyMessage,
    this.fileSize,
  });

  @override
  State<FileMessageWidget> createState() => _FileMessageWidgetState();
}

class _FileMessageWidgetState extends State<FileMessageWidget> {
  bool _isDownloading = false;
  double _downloadProgress = 0.0;

  @override
  Widget build(BuildContext context) {
    final fileExtension = widget.fileName.split('.').last.toLowerCase();
    final mimeType = lookupMimeType(widget.fileName);
    final screenWidth = MediaQuery.of(context).size.width;
    final maxWidth = screenWidth > 600 ? 350.0 : screenWidth * 0.8;

    return Semantics(
      label: 'ไฟล์แนบ: ${widget.fileName}',
      hint: 'แตะเพื่อดาวน์โหลดและเปิดไฟล์',
      child: Container(
        constraints: BoxConstraints(maxWidth: maxWidth),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: widget.isMyMessage
              ? Colors.white.withOpacity(0.1)
              : Colors.grey[100],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                _buildFileIcon(fileExtension, mimeType),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.fileName,
                        style: TextStyle(
                          color: widget.isMyMessage
                              ? Colors.white
                              : Colors.black87,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (widget.fileSize != null) ...[
                        const SizedBox(height: 2),
                        Text(
                          _formatFileSize(widget.fileSize!),
                          style: TextStyle(
                            color: widget.isMyMessage
                                ? Colors.white70
                                : Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                _buildActionButton(),
              ],
            ),
            if (_isDownloading) ...[
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: _downloadProgress,
                backgroundColor: widget.isMyMessage
                    ? Colors.white.withOpacity(0.3)
                    : Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  widget.isMyMessage ? Colors.white : Colors.blue,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFileIcon(String extension, String? mimeType) {
    IconData iconData;
    Color iconColor;

    if (mimeType?.startsWith('image/') == true) {
      iconData = Icons.image;
      iconColor = Colors.green;
    } else if (mimeType?.startsWith('video/') == true) {
      iconData = Icons.video_file;
      iconColor = Colors.red;
    } else if (mimeType?.startsWith('audio/') == true) {
      iconData = Icons.audio_file;
      iconColor = Colors.orange;
    } else if (extension == 'pdf') {
      iconData = Icons.picture_as_pdf;
      iconColor = Colors.red;
    } else if (['doc', 'docx'].contains(extension)) {
      iconData = Icons.description;
      iconColor = Colors.blue;
    } else if (['xls', 'xlsx'].contains(extension)) {
      iconData = Icons.table_chart;
      iconColor = Colors.green;
    } else if (['ppt', 'pptx'].contains(extension)) {
      iconData = Icons.slideshow;
      iconColor = Colors.orange;
    } else if (['zip', 'rar', '7z'].contains(extension)) {
      iconData = Icons.archive;
      iconColor = Colors.purple;
    } else {
      iconData = Icons.insert_drive_file;
      iconColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: iconColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(iconData, color: iconColor, size: 24),
    );
  }

  Widget _buildActionButton() {
    if (_isDownloading) {
      return SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            widget.isMyMessage ? Colors.white : Colors.blue,
          ),
        ),
      );
    }

    return IconButton(
      onPressed: _downloadAndOpenFile,
      icon: Icon(
        Icons.download,
        color: widget.isMyMessage ? Colors.white : Colors.blue,
        size: 20,
      ),
      padding: EdgeInsets.zero,
      constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  Future<void> _downloadAndOpenFile() async {
    if (_isDownloading) return;

    setState(() {
      _isDownloading = true;
      _downloadProgress = 0.0;
    });

    try {
      final directory = await getTemporaryDirectory();
      final filePath = '${directory.path}/${widget.fileName}';
      final file = File(filePath);

      // Check if file already exists
      if (await file.exists()) {
        await _openFile(filePath);
        return;
      }

      // Download file
      final response = await http.get(Uri.parse(widget.fileUrl));

      if (response.statusCode == 200) {
        await file.writeAsBytes(response.bodyBytes);
        await _openFile(filePath);
      } else {
        throw Exception('Failed to download file: ${response.statusCode}');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('ไม่สามารถดาวน์โหลดไฟล์ได้: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
          _downloadProgress = 0.0;
        });
      }
    }
  }

  Future<void> _openFile(String filePath) async {
    try {
      final result = await OpenFilex.open(filePath);
      if (result.type != ResultType.done && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('ไม่สามารถเปิดไฟล์ได้: ${result.message}'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('ไม่สามารถเปิดไฟล์ได้: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
