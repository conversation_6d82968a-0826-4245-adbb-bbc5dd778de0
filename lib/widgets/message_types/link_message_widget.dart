import 'package:flutter/material.dart';
import 'package:any_link_preview/any_link_preview.dart';

class LinkMessageWidget extends StatelessWidget {
  final String linkUrl;
  final bool isMyMessage;

  const LinkMessageWidget({
    super.key,
    required this.linkUrl,
    required this.isMyMessage,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final maxWidth = screenWidth > 600 ? 320.0 : screenWidth * 0.85;
    
    return Semantics(
      label: 'ลิงก์: $linkUrl',
      hint: 'แตะเพื่อเปิดลิงก์',
      child: Container(
        constraints: BoxConstraints(maxWidth: maxWidth),
        child: AnyLinkPreview(
          link: linkUrl,
          displayDirection: UIDirection.uiDirectionVertical,
          showMultimedia: true,
          bodyMaxLines: 2,
          bodyTextOverflow: TextOverflow.ellipsis,
          titleStyle: TextStyle(
            color: isMyMessage ? Colors.white : Colors.black87,
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
          bodyStyle: TextStyle(
            color: isMyMessage ? Colors.white70 : Colors.grey[600],
            fontSize: 12,
          ),
          backgroundColor: isMyMessage 
              ? Colors.white.withOpacity(0.1)
              : Colors.grey[100],
          borderRadius: 12,
          removeElevation: false,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              spreadRadius: 1,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
          errorTitle: 'ไม่สามารถโหลดลิงก์ได้',
          errorBody: 'แตะเพื่อเปิดลิงก์โดยตรง',
          errorWidget: _buildErrorWidget(),
          placeholderWidget: _buildLoadingWidget(),
          cache: const Duration(hours: 1),
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isMyMessage 
            ? Colors.white.withOpacity(0.1)
            : Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isMyMessage 
              ? Colors.white.withOpacity(0.3)
              : Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.link,
            color: isMyMessage ? Colors.white70 : Colors.grey[600],
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'ลิงก์',
                  style: TextStyle(
                    color: isMyMessage ? Colors.white : Colors.black87,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  linkUrl,
                  style: TextStyle(
                    color: isMyMessage ? Colors.white : Colors.blue,
                    fontSize: 12,
                    decoration: TextDecoration.underline,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isMyMessage 
            ? Colors.white.withOpacity(0.1)
            : Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isMyMessage 
              ? Colors.white.withOpacity(0.3)
              : Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                isMyMessage ? Colors.white70 : Colors.grey[400]!,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'กำลังโหลดตัวอย่างลิงก์...',
                  style: TextStyle(
                    color: isMyMessage ? Colors.white70 : Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  linkUrl,
                  style: TextStyle(
                    color: isMyMessage ? Colors.white : Colors.blue,
                    fontSize: 12,
                    decoration: TextDecoration.underline,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
