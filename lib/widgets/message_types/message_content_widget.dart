import 'package:flutter/material.dart';
import 'package:hia_sang_ma/models/chat_model.dart';
import 'package:hia_sang_ma/widgets/message_types/text_message_widget.dart';
import 'package:hia_sang_ma/widgets/message_types/image_message_widget.dart';
import 'package:hia_sang_ma/widgets/message_types/file_message_widget.dart';
import 'package:hia_sang_ma/widgets/message_types/sticker_message_widget.dart';
import 'package:hia_sang_ma/widgets/message_types/link_message_widget.dart';
import 'dart:convert';

class MessageContentWidget extends StatelessWidget {
  final ChatMessage message;
  final bool isMyMessage;

  const MessageContentWidget({
    super.key,
    required this.message,
    required this.isMyMessage,
  });

  @override
  Widget build(BuildContext context) {
    try {
      switch (message.messageType) {
        case MessageType.text:
          return TextMessageWidget(
            content: message.content,
            isMyMessage: isMyMessage,
          );

        case MessageType.image:
          return _buildImageMessage();

        case MessageType.file:
          return _buildFileMessage();

        case MessageType.sticker:
          return StickerMessageWidget(
            stickerUrl: message.content,
            isMyMessage: isMyMessage,
          );

        case MessageType.link:
          return _buildLinkMessage();

        default:
          // Fallback to text message for unknown types
          return TextMessageWidget(
            content: message.content,
            isMyMessage: isMyMessage,
          );
      }
    } catch (e) {
      // Error handling - show error message
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, color: Colors.red[700], size: 16),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'ไม่สามารถแสดงข้อความได้',
                style: TextStyle(color: Colors.red[700], fontSize: 12),
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildImageMessage() {
    try {
      // Try to parse content as JSON for structured image data
      final Map<String, dynamic>? imageData = _tryParseJson(message.content);

      if (imageData != null && imageData['url'] != null) {
        return ImageMessageWidget(
          imageUrl: imageData['url'],
          isMyMessage: isMyMessage,
        );
      } else {
        // Assume content is direct image URL
        return ImageMessageWidget(
          imageUrl: message.content,
          isMyMessage: isMyMessage,
        );
      }
    } catch (e) {
      return _buildErrorWidget('ไม่สามารถแสดงรูปภาพได้');
    }
  }

  Widget _buildFileMessage() {
    try {
      // Try to parse content as JSON for structured file data
      final Map<String, dynamic>? fileData = _tryParseJson(message.content);

      if (fileData != null) {
        return FileMessageWidget(
          fileUrl: fileData['url'] ?? message.content,
          fileName: fileData['name'] ?? 'ไฟล์',
          fileSize: fileData['size'],
          isMyMessage: isMyMessage,
        );
      } else {
        // Assume content is direct file URL
        final fileName = _extractFileNameFromUrl(message.content);
        return FileMessageWidget(
          fileUrl: message.content,
          fileName: fileName,
          isMyMessage: isMyMessage,
        );
      }
    } catch (e) {
      return _buildErrorWidget('ไม่สามารถแสดงไฟล์ได้');
    }
  }

  Widget _buildLinkMessage() {
    try {
      // Try to parse content as JSON for structured link data
      final Map<String, dynamic>? linkData = _tryParseJson(message.content);

      if (linkData != null && linkData['url'] != null) {
        return LinkMessageWidget(
          linkUrl: linkData['url'],
          isMyMessage: isMyMessage,
        );
      } else {
        // Assume content is direct URL
        return LinkMessageWidget(
          linkUrl: message.content,
          isMyMessage: isMyMessage,
        );
      }
    } catch (e) {
      return _buildErrorWidget('ไม่สามารถแสดงลิงก์ได้');
    }
  }

  Widget _buildErrorWidget(String errorMessage) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.error_outline, color: Colors.red[700], size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              errorMessage,
              style: TextStyle(color: Colors.red[700], fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Map<String, dynamic>? _tryParseJson(String content) {
    try {
      return json.decode(content) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  String _extractFileNameFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      final segments = uri.pathSegments;
      if (segments.isNotEmpty) {
        return segments.last;
      }
    } catch (e) {
      // Ignore parsing errors
    }
    return 'ไฟล์';
  }
}
