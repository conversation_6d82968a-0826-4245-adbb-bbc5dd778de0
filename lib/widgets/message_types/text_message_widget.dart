import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:hia_sang_ma/constants/app_colors.dart';

class TextMessageWidget extends StatelessWidget {
  final String content;
  final bool isMyMessage;

  const TextMessageWidget({
    super.key,
    required this.content,
    required this.isMyMessage,
  });

  @override
  Widget build(BuildContext context) {
    // Handle empty or null content
    if (content.trim().isEmpty) {
      return Semantics(
        label: 'ข้อความว่าง',
        child: Text(
          'ไม่มีเนื้อหา',
          style: TextStyle(
            color: isMyMessage ? Colors.white70 : Colors.grey[500],
            fontSize: 14,
            fontStyle: FontStyle.italic,
          ),
        ),
      );
    }

    // Decode HTML entities first
    String decodedContent = _decodeHtmlEntities(content);

    // Check if content contains HTML tags
    final containsHtml = RegExp(r'<[^>]*>').hasMatch(decodedContent);

    if (!containsHtml) {
      // If no HTML tags, display as plain text
      return Text(
        decodedContent,
        style: TextStyle(
          color: isMyMessage ? Colors.black : Colors.black87,
          fontSize: 14,
        ),
      );
    }

    // Display HTML content
    return Html(
      data: decodedContent,
      style: {
        "body": Style(
          fontSize: FontSize(14),
          color: isMyMessage ? Colors.black : Colors.black87,
          margin: Margins.zero,
          padding: HtmlPaddings.zero,
        ),
        "p": Style(margin: Margins.zero, padding: HtmlPaddings.zero),
        "div": Style(margin: Margins.zero, padding: HtmlPaddings.zero),
        "a": Style(
          color: isMyMessage ? Colors.black : AppColors.primary,
          textDecoration: TextDecoration.underline,
        ),
        "strong": Style(fontWeight: FontWeight.bold),
        "em": Style(fontStyle: FontStyle.italic),
        "br": Style(margin: Margins.only(bottom: 4)),
        "img": Style(maxLines: 1, width: Width(200), height: Height(150)),
      },
      onLinkTap: (url, _, __) {
        _handleLinkTap(context, url);
      },
    );
  }

  /// Decode common HTML entities to their Unicode equivalents
  String _decodeHtmlEntities(String input) {
    return input
        .replaceAll('&nbsp;', ' ') // Non-breaking space
        .replaceAll('&amp;', '&') // Ampersand
        .replaceAll('&lt;', '<') // Less than
        .replaceAll('&gt;', '>') // Greater than
        .replaceAll('&quot;', '"') // Double quote
        .replaceAll('&#39;', "'") // Single quote
        .replaceAll('&apos;', "'") // Apostrophe
        .replaceAll('&cent;', '¢') // Cent
        .replaceAll('&pound;', '£') // Pound
        .replaceAll('&yen;', '¥') // Yen
        .replaceAll('&euro;', '€') // Euro
        .replaceAll('&copy;', '©') // Copyright
        .replaceAll('&reg;', '®'); // Registered trademark
  }

  Future<void> _handleLinkTap(BuildContext context, String? url) async {
    if (url == null || url.isEmpty) return;

    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        // Show error message if URL cannot be launched
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('ไม่สามารถเปิดลิงก์ได้: $url'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // Handle invalid URL format
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('ลิงก์ไม่ถูกต้อง: $url'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
