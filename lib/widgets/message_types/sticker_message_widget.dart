import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class StickerMessageWidget extends StatelessWidget {
  final String stickerUrl;
  final bool isMyMessage;

  const StickerMessageWidget({
    super.key,
    required this.stickerUrl,
    required this.isMyMessage,
  });

  @override
  Widget build(BuildContext context) {
    // Check if it's an emoji (Unicode character) or URL
    final isEmoji = _isEmoji(stickerUrl);
    final screenWidth = MediaQuery.of(context).size.width;
    final stickerSize = screenWidth > 600 ? 120.0 : 100.0;

    return Semantics(
      label: isEmoji ? 'อีโมจิ: $stickerUrl' : 'สติกเกอร์',
      hint: 'แตะเพื่อดูขนาดใหญ่',
      child: GestureDetector(
        onTap: () => _showStickerPreview(context),
        child: Container(
          constraints: BoxConstraints(
            maxWidth: stickerSize,
            maxHeight: stickerSize,
          ),
          child: isEmoji ? _buildEmojiSticker() : _buildImageSticker(),
        ),
      ),
    );
  }

  Widget _buildEmojiSticker() {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Text(
        stickerUrl,
        style: const TextStyle(fontSize: 64),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildImageSticker() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: CachedNetworkImage(
        imageUrl: stickerUrl,
        fit: BoxFit.contain,
        placeholder: (context, url) => Container(
          width: 120,
          height: 120,
          color: Colors.transparent,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    isMyMessage ? Colors.white70 : Colors.grey[400]!,
                  ),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'กำลังโหลด...',
                style: TextStyle(
                  fontSize: 10,
                  color: isMyMessage ? Colors.white70 : Colors.grey[500],
                ),
              ),
            ],
          ),
        ),
        errorWidget: (context, url, error) => Container(
          width: 120,
          height: 120,
          decoration: BoxDecoration(
            color: Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.sentiment_dissatisfied,
                size: 32,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 4),
              Text(
                'ไม่สามารถโหลด\nสติกเกอร์ได้',
                style: TextStyle(fontSize: 10, color: Colors.grey[500]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  bool _isEmoji(String text) {
    // Simple check for emoji - this is a basic implementation
    // You might want to use a more sophisticated emoji detection library
    final emojiRegex = RegExp(
      r'[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]',
      unicode: true,
    );

    return emojiRegex.hasMatch(text) &&
        text.length <= 4 &&
        !text.startsWith('http');
  }

  void _showStickerPreview(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: GestureDetector(
          onTap: () => Navigator.of(context).pop(),
          child: Container(
            constraints: const BoxConstraints(maxWidth: 300, maxHeight: 300),
            child: _isEmoji(stickerUrl)
                ? Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      stickerUrl,
                      style: const TextStyle(fontSize: 120),
                      textAlign: TextAlign.center,
                    ),
                  )
                : ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: CachedNetworkImage(
                      imageUrl: stickerUrl,
                      fit: BoxFit.contain,
                      placeholder: (context, url) => Container(
                        width: 200,
                        height: 200,
                        color: Colors.white,
                        child: const Center(child: CircularProgressIndicator()),
                      ),
                      errorWidget: (context, url, error) => Container(
                        width: 200,
                        height: 200,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: const Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.sentiment_dissatisfied,
                              size: 64,
                              color: Colors.grey,
                            ),
                            SizedBox(height: 16),
                            Text(
                              'ไม่สามารถโหลดสติกเกอร์ได้',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),
        ),
      ),
    );
  }
}
