import 'package:flutter/material.dart';

/// A reusable notification badge widget that displays a count over another widget
class NotificationBadge extends StatelessWidget {
  /// The child widget to display the badge over
  final Widget child;

  /// The count to display in the badge
  final int count;

  /// Whether to show the badge even when count is 0
  final bool showZero;

  /// The background color of the badge
  final Color backgroundColor;

  /// The text color of the badge
  final Color textColor;

  /// The size of the badge
  final double badgeSize;

  /// The font size of the badge text
  final double fontSize;

  /// The position offset from the top-right corner
  final Offset offset;

  const NotificationBadge({
    super.key,
    required this.child,
    required this.count,
    this.showZero = false,
    this.backgroundColor = Colors.red,
    this.textColor = Colors.white,
    this.badgeSize = 18.0,
    this.fontSize = 10.0,
    this.offset = const Offset(-4, 4),
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        child,
        if (count > 0 || showZero)
          Positioned(
            right: offset.dx,
            top: offset.dy,
            child: Container(
              width: badgeSize,
              height: badgeSize,
              padding: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(badgeSize / 2),
                border: Border.all(color: Colors.white, width: 1),
              ),
              constraints: BoxConstraints(
                minWidth: badgeSize,
                minHeight: badgeSize,
              ),
              child: Center(
                child: Text(
                  _getDisplayText(),
                  style: TextStyle(
                    color: textColor,
                    fontSize: fontSize,
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Get the text to display in the badge
  String _getDisplayText() {
    if (count > 99) {
      return '99+';
    }
    return count.toString();
  }
}

/// A specialized notification badge for navigation items
class NavigationNotificationBadge extends StatelessWidget {
  /// The navigation icon to display the badge over
  final Widget icon;

  /// The count to display in the badge
  final int count;

  const NavigationNotificationBadge({
    super.key,
    required this.icon,
    required this.count,
  });

  @override
  Widget build(BuildContext context) {
    return NotificationBadge(
      count: count,
      backgroundColor: Colors.red,
      textColor: Colors.white,
      badgeSize: 16.0,
      fontSize: 9.0,
      offset: const Offset(-2, 2),
      child: icon,
    );
  }
}

/// A notification badge specifically designed for bottom navigation items
class BottomNavNotificationBadge extends StatelessWidget {
  /// The navigation icon to display the badge over
  final Widget icon;

  /// The count to display in the badge
  final int count;

  /// Whether the navigation item is currently selected
  final bool isSelected;

  const BottomNavNotificationBadge({
    super.key,
    required this.icon,
    required this.count,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: icon,
        ),
        if (count > 0)
          Positioned(
            right: -2,
            top: 0,
            child: Container(
              width: 14,
              height: 14,
              padding: const EdgeInsets.symmetric(horizontal: 3),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(7),
                border: Border.all(color: Colors.white, width: 1),
              ),
              constraints: const BoxConstraints(minWidth: 14, minHeight: 14),
              child: Center(
                child: Text(
                  count > 99 ? '99+' : count.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 8,
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
