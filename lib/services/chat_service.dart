import 'dart:convert';
import 'package:hia_sang_ma/services/api_service.dart';
import 'package:hia_sang_ma/services/auth_service.dart';
import 'package:hia_sang_ma/models/chat_model.dart';

class ChatService {
  final AuthService _authService = AuthService();

  /// Get list of chats with optional filtering
  Future<List<ChatModel>> getChats({
    int? id,
    ChatType? chatType,
    int? userId,
    int? organizationId,
    int? departmentId,
    int? taskId,
  }) async {
    final token = await _authService.getToken();
    if (token == null) throw Exception('Authentication required');

    try {
      final queryParams = <String, String>{};

      if (id != null) queryParams['id'] = id.toString();
      if (chatType != null) queryParams['chatType'] = chatType.name;
      if (userId != null) queryParams['userId'] = userId.toString();
      if (organizationId != null)
        queryParams['organizationId'] = organizationId.toString();
      if (departmentId != null)
        queryParams['departmentId'] = departmentId.toString();
      if (taskId != null) queryParams['taskId'] = taskId.toString();

      final response = await ApiService.get(
        '/chat',
        token: token,
        queryParams: queryParams.isNotEmpty ? queryParams : null,
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);

        // Handle different response formats
        List<dynamic> chatsData;
        if (responseData is List) {
          chatsData = responseData;
        } else if (responseData is Map<String, dynamic>) {
          // Handle the new API response format with "chats" key
          chatsData = responseData['chats'] ?? responseData['data'] ?? [];
        } else {
          throw Exception('Unexpected response format');
        }

        return chatsData
            .map((chatJson) => ChatModel.fromJson(chatJson))
            .toList();
      } else if (response.statusCode == 401) {
        await _authService.logout();
        throw Exception('Authentication expired');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to fetch chats');
      }
    } catch (e) {
      if (e.toString().contains('Authentication')) {
        rethrow;
      }
      throw Exception('Network error: ${e.toString()}');
    }
  }

  /// Get a specific chat by ID
  Future<ChatModel?> getChatById(int chatId) async {
    try {
      final chats = await getChats(id: chatId);
      return chats.isNotEmpty ? chats.first : null;
    } catch (e) {
      throw Exception('Failed to fetch chat: ${e.toString()}');
    }
  }

  /// Get chat messages for a specific chat
  Future<List<ChatMessage>> getChatMessages({
    required int chatId,
    int? messageId,
    int page = 1,
    int limit = 20,
    int? before,
    int? after,
    bool includeReadStatus = false,
    String orderBy = 'created',
    String orderDirection = 'DESC',
  }) async {
    final token = await _authService.getToken();
    if (token == null) throw Exception('Authentication required');

    try {
      final queryParams = <String, String>{
        'chatId': chatId.toString(),
        'page': page.toString(),
        'limit': limit.toString(),
        'orderBy': orderBy,
        'orderDirection': orderDirection,
      };

      if (messageId != null) queryParams['id'] = messageId.toString();
      if (before != null) queryParams['before'] = before.toString();
      if (after != null) queryParams['after'] = after.toString();
      if (includeReadStatus) queryParams['includeReadStatus'] = 'true';

      final response = await ApiService.get(
        '/chat-message',
        token: token,
        queryParams: queryParams,
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);

        // Handle different response formats
        List<dynamic> messagesData;
        if (responseData is List) {
          messagesData = responseData;
        } else if (responseData is Map<String, dynamic>) {
          // If response has pagination or wrapper
          messagesData = responseData['data'] ?? responseData['messages'] ?? [];
        } else {
          throw Exception('Unexpected response format');
        }

        return messagesData
            .map((messageJson) => ChatMessage.fromJson(messageJson))
            .toList();
      } else if (response.statusCode == 401) {
        await _authService.logout();
        throw Exception('Authentication expired');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(errorData['error'] ?? 'Failed to fetch messages');
      }
    } catch (e) {
      if (e.toString().contains('Authentication')) {
        rethrow;
      }
      throw Exception('Network error: ${e.toString()}');
    }
  }

  /// Get unread message count for a specific chat
  Future<int> getUnreadMessageCountForChat(int chatId) async {
    final token = await _authService.getToken();
    if (token == null) throw Exception('Authentication required');

    try {
      final response = await ApiService.get(
        '/chat-message/unread-count',
        token: token,
        queryParams: {'chatId': chatId.toString()},
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData['unreadCount'] ?? 0;
      } else if (response.statusCode == 401) {
        await _authService.logout();
        throw Exception('Authentication expired');
      } else {
        return 0; // Return 0 if we can't get the count
      }
    } catch (e) {
      if (e.toString().contains('Authentication')) {
        rethrow;
      }
      return 0; // Return 0 on error
    }
  }

  /// Get unread message counts for all user's chats
  Future<List<UnreadChatInfo>> getAllUnreadMessageCounts() async {
    final token = await _authService.getToken();
    if (token == null) throw Exception('Authentication required');

    try {
      final response = await ApiService.get(
        '/chat-message/unread-count',
        token: token,
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        final unreadCounts =
            responseData['unreadCounts'] as List<dynamic>? ?? [];

        return unreadCounts
            .map((item) => UnreadChatInfo.fromJson(item))
            .toList();
      } else if (response.statusCode == 401) {
        await _authService.logout();
        throw Exception('Authentication expired');
      } else {
        return []; // Return empty list if we can't get the counts
      }
    } catch (e) {
      if (e.toString().contains('Authentication')) {
        rethrow;
      }
      return []; // Return empty list on error
    }
  }

  /// Get total unread message count across all chats
  Future<int> getTotalUnreadMessageCount() async {
    final token = await _authService.getToken();
    if (token == null) throw Exception('Authentication required');

    try {
      final response = await ApiService.get(
        '/chat-message/unread-count',
        token: token,
        queryParams: {'total': 'true'},
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData['totalUnreadCount'] ?? 0;
      } else if (response.statusCode == 401) {
        await _authService.logout();
        throw Exception('Authentication expired');
      } else {
        return 0; // Return 0 if we can't get the count
      }
    } catch (e) {
      if (e.toString().contains('Authentication')) {
        rethrow;
      }
      return 0; // Return 0 on error
    }
  }

  /// Get unread message count for a user (legacy method for backward compatibility)
  Future<int> getUnreadMessageCount() async {
    return await getTotalUnreadMessageCount();
  }

  /// Mark messages as read
  Future<Map<String, dynamic>> markMessagesAsRead({
    int? messageId,
    int? chatId,
    int? beforeMessageId,
  }) async {
    final token = await _authService.getToken();
    if (token == null) throw Exception('Authentication required');

    try {
      final body = <String, dynamic>{};

      if (messageId != null) {
        body['messageId'] = messageId;
      } else if (chatId != null) {
        body['chatId'] = chatId;
        if (beforeMessageId != null) {
          body['beforeMessageId'] = beforeMessageId;
        }
      } else {
        throw Exception('Either messageId or chatId must be provided');
      }

      final response = await ApiService.post(
        '/chat-message/mark-read',
        body,
        token: token,
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        return responseData;
      } else if (response.statusCode == 401) {
        await _authService.logout();
        throw Exception('Authentication expired');
      } else {
        final errorData = json.decode(response.body);
        throw Exception(
          errorData['error'] ?? 'Failed to mark messages as read',
        );
      }
    } catch (e) {
      if (e.toString().contains('Authentication')) {
        rethrow;
      }
      throw Exception('Network error: ${e.toString()}');
    }
  }

  /// Get unread counts grouped by chat type
  Future<Map<ChatType, int>> getUnreadCountsByType() async {
    try {
      final unreadCounts = await getAllUnreadMessageCounts();
      final groupedCounts = <ChatType, int>{};

      // Initialize all chat types with 0
      for (final chatType in ChatType.values) {
        groupedCounts[chatType] = 0;
      }

      // Sum unread counts by chat type
      for (final unreadInfo in unreadCounts) {
        groupedCounts[unreadInfo.chatType] =
            (groupedCounts[unreadInfo.chatType] ?? 0) + unreadInfo.unreadCount;
      }

      return groupedCounts;
    } catch (e) {
      // Return all zeros on error
      final groupedCounts = <ChatType, int>{};
      for (final chatType in ChatType.values) {
        groupedCounts[chatType] = 0;
      }
      return groupedCounts;
    }
  }

  /// Get chats filtered by type with enhanced data
  Future<List<ChatModel>> getChatsByType(ChatType chatType) async {
    try {
      final chats = await getChats(chatType: chatType);

      // Sort chats by last message time (most recent first)
      chats.sort((a, b) {
        final aTime = a.lastMessage?.createdAt ?? a.updatedAt;
        final bTime = b.lastMessage?.createdAt ?? b.updatedAt;
        return bTime.compareTo(aTime);
      });

      return chats;
    } catch (e) {
      throw Exception(
        'Failed to fetch ${chatType.displayName} chats: ${e.toString()}',
      );
    }
  }

  /// Get all chats grouped by type
  Future<Map<ChatType, List<ChatModel>>> getAllChatsGroupedByType() async {
    try {
      final allChats = await getChats();
      final groupedChats = <ChatType, List<ChatModel>>{};

      // Initialize all chat types
      for (final chatType in ChatType.values) {
        groupedChats[chatType] = [];
      }

      // Group chats by type
      for (final chat in allChats) {
        groupedChats[chat.chatType]?.add(chat);
      }

      // Sort each group by last message time
      for (final chatType in ChatType.values) {
        groupedChats[chatType]?.sort((a, b) {
          final aTime = a.lastMessage?.createdAt ?? a.updatedAt;
          final bTime = b.lastMessage?.createdAt ?? b.updatedAt;
          return bTime.compareTo(aTime);
        });
      }

      return groupedChats;
    } catch (e) {
      throw Exception('Failed to fetch grouped chats: ${e.toString()}');
    }
  }
}
