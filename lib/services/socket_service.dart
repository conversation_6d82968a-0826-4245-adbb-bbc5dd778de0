import 'dart:async';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:hia_sang_ma/config/socket_config.dart';

/// Core Socket.IO service for managing connections and events
class SocketService {
  static final SocketService _instance = SocketService._internal();
  factory SocketService() => _instance;
  SocketService._internal();

  IO.Socket? _socket;
  bool _isConnected = false;
  String? _userId;
  int _reconnectAttempts = 0;
  Timer? _reconnectTimer;

  /// Stream controller for connection status changes
  final StreamController<bool> _connectionController =
      StreamController<bool>.broadcast();

  /// Stream controller for connection errors
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  /// Getters
  bool get isConnected => _isConnected;
  String? get userId => _userId;
  Stream<bool> get connectionStream => _connectionController.stream;
  Stream<String> get errorStream => _errorController.stream;

  /// Initialize and connect to Socket.IO server
  Future<void> connect(String userId) async {
    if (_socket?.connected == true) {
      await disconnect();
    }

    _userId = userId;
    _reconnectAttempts = 0;

    try {
      final socketUrl = SocketConfig.getSocketUrl();
      final query = SocketConfig.getDefaultQuery(userId);

      if (SocketConfig.enableLogging) {
        print('🔧 Socket URL: $socketUrl');
        print('🔧 Query params: $query');
        print('🔧 Transports: ${SocketConfig.transports}');
      }

      _socket = IO.io(
        socketUrl,
        IO.OptionBuilder()
            .setTransports(SocketConfig.transports)
            .enableAutoConnect()
            .setTimeout(SocketConfig.connectionTimeout.inMilliseconds)
            .setQuery(query)
            .enableForceNew()
            .setReconnectionAttempts(SocketConfig.maxReconnectAttempts)
            .setReconnectionDelay(SocketConfig.reconnectionDelay.inMilliseconds)
            .enableReconnection()
            .setRandomizationFactor(0.5)
            .build(),
      );

      _setupEventListeners();
      _socket?.connect();

      if (SocketConfig.enableLogging) {
        print('🔌 Socket connecting to: ${SocketConfig.getSocketUrl()}');
        print('👤 User ID: $userId');
      }
    } catch (e) {
      _handleConnectionError('Failed to initialize socket: $e');
    }
  }

  /// Setup core socket event listeners
  void _setupEventListeners() {
    _socket?.on('connect', (data) {
      if (SocketConfig.enableLogging) {
        print('✅ Connected to socket server');
      }
      _isConnected = true;
      _reconnectAttempts = 0;
      _connectionController.add(true);
    });

    _socket?.on('disconnect', (data) {
      if (SocketConfig.enableLogging) {
        print('❌ Disconnected from socket server: $data');
      }
      _isConnected = false;
      _connectionController.add(false);
    });

    _socket?.on('connect_error', (error) {
      if (SocketConfig.enableLogging) {
        print('🚫 Socket connection error: $error');
      }
      _isConnected = false;
      _connectionController.add(false);
      _handleConnectionError('Connection error: $error');
    });

    _socket?.on('error', (error) {
      if (SocketConfig.enableLogging) {
        print('⚠️ Socket error: $error');
      }
      _errorController.add('Socket error: $error');
    });

    _socket?.on('reconnect', (data) {
      if (SocketConfig.enableLogging) {
        print('🔄 Socket reconnected: $data');
      }
      _isConnected = true;
      _reconnectAttempts = 0;
      _connectionController.add(true);
    });

    _socket?.on('reconnect_error', (error) {
      if (SocketConfig.enableLogging) {
        print('🔄❌ Socket reconnection error: $error');
      }
      _handleConnectionError('Reconnection error: $error');
    });
  }

  /// Handle connection errors with retry logic
  void _handleConnectionError(String error) {
    _errorController.add(error);

    if (_reconnectAttempts < SocketConfig.maxReconnectAttempts) {
      _reconnectAttempts++;

      if (SocketConfig.enableLogging) {
        print(
          '🔄 Attempting reconnection $_reconnectAttempts/${SocketConfig.maxReconnectAttempts}',
        );
      }

      _reconnectTimer?.cancel();
      _reconnectTimer = Timer(
        Duration(
          seconds:
              _reconnectAttempts * SocketConfig.reconnectionDelay.inSeconds,
        ),
        () => _attemptReconnection(),
      );
    } else {
      if (SocketConfig.enableLogging) {
        print('💀 Max reconnection attempts reached');
      }
      _errorController.add('Max reconnection attempts reached');
    }
  }

  /// Attempt to reconnect to the server
  void _attemptReconnection() async {
    if (_userId != null) {
      try {
        await connect(_userId!);
      } catch (e) {
        _handleConnectionError('Reconnection failed: $e');
      }
    }
  }

  /// Emit event to server
  void emit(String event, dynamic data) {
    if (_socket?.connected == true) {
      _socket?.emit(event, data);
      if (SocketConfig.enableLogging) {
        print('📤 Emitted event: $event');
        print('📦 Data: $data');
      }
    } else {
      if (SocketConfig.enableLogging) {
        print('⚠️ Socket not connected, cannot emit: $event');
      }
      _errorController.add('Socket not connected, cannot emit: $event');
    }
  }

  /// Emit event to server with acknowledgment
  void emitWithAck(String event, dynamic data, Function(dynamic)? callback) {
    if (_socket?.connected == true) {
      _socket?.emitWithAck(event, data, ack: callback);
      if (SocketConfig.enableLogging) {
        print('📤 Emitted event with ack: $event');
        print('📦 Data: $data');
      }
    } else {
      if (SocketConfig.enableLogging) {
        print('⚠️ Socket not connected, cannot emit: $event');
      }
      _errorController.add('Socket not connected, cannot emit: $event');
      // Call callback with error if provided
      callback?.call({'error': 'Socket not connected'});
    }
  }

  /// Listen to server events
  void on(String event, Function(dynamic) callback) {
    if (SocketConfig.enableLogging) {
      print('🎯 Registering listener for event: $event');
    }
    _socket?.on(event, (data) {
      if (SocketConfig.enableLogging) {
        print('📥 Received event: $event');
        print('📦 Data: $data');
      }
      callback(data);
    });
  }

  /// Listen to all events for debugging (without overriding existing listeners)
  void enableDebugMode() {
    if (_socket != null) {
      // Listen to debug events that don't conflict with main functionality
      final debugEvents = [
        // 'chat_notification', // Removed - handled by ChatSocketService
        'notification',
        'messages_read',
        'message_read',
        'receive_join',
        'user_joined',
        'join',
        'receive_leave',
        'user_left',
        'leave',
        'unread_count_updated',
        'unread_count',
        'send_notification',
        'general_notification',
      ];

      for (String event in debugEvents) {
        _socket?.on(event, (data) {
          print('🔍 DEBUG: Received event "$event" with data: $data');
        });
      }

      // For new_message, just add additional logging without overriding
      print('🔧 Debug mode enabled (without overriding main event listeners)');
    }
  }

  /// Remove event listener
  void off(String event) {
    _socket?.off(event);
    if (SocketConfig.enableLogging) {
      print('🔇 Removed listener for: $event');
    }
  }

  /// Disconnect from socket server
  Future<void> disconnect() async {
    _reconnectTimer?.cancel();

    if (_socket != null) {
      _socket?.disconnect();
      _socket?.dispose();
      _socket = null;
      _isConnected = false;
      _userId = null;
      _reconnectAttempts = 0;

      _connectionController.add(false);

      if (SocketConfig.enableLogging) {
        print('🔌❌ Socket disconnected and disposed');
      }
    }
  }

  /// Dispose resources
  void dispose() {
    disconnect();
    _connectionController.close();
    _errorController.close();
  }
}
