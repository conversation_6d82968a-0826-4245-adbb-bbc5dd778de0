# Socket.IO Chat Implementation Guide for Flutter

## Overview
This document provides a comprehensive guide for implementing Socket.IO chat functionality in a Flutter project based on the Node.js/React chat system. It covers all socket events, data structures, and implementation patterns necessary for real-time chat communication.

## Table of Contents
1. [Dependencies and Setup](#dependencies-and-setup)
2. [Socket Connection Management](#socket-connection-management)
3. [Socket Events Overview](#socket-events-overview)
4. [Data Models](#data-models)
5. [Client-Side Socket Events](#client-side-socket-events)
6. [Server-Side Socket Events](#server-side-socket-events)
7. [Chat Message Flow](#chat-message-flow)
8. [Real-time Features](#real-time-features)
9. [Error Handling](#error-handling)
10. [Best Practices](#best-practices)
11. [Example Implementation](#example-implementation)

## Dependencies and Setup

### 1. Add Dependencies
Add these dependencies to your `pubspec.yaml`:

```yaml
dependencies:
  socket_io_client: ^2.0.3+1
  flutter_bloc: ^8.1.3  # for state management
  json_annotation: ^4.8.1
  
dev_dependencies:
  json_serializable: ^6.7.1
  build_runner: ^2.4.7
```

### 2. Socket Configuration
```dart
// lib/config/socket_config.dart
class SocketConfig {
  static const String serverUrl = 'ws://your-server-url:8081';
  static const Duration connectionTimeout = Duration(seconds: 10);
  static const Duration reconnectionDelay = Duration(seconds: 2);
  static const int maxReconnectAttempts = 5;
}
```

## Socket Connection Management

### 1. Socket Service Class
```dart
// lib/services/socket_service.dart
import 'package:socket_io_client/socket_io_client.dart' as IO;

class SocketService {
  static final SocketService _instance = SocketService._internal();
  factory SocketService() => _instance;
  SocketService._internal();

  IO.Socket? _socket;
  bool _isConnected = false;
  String? _userId;

  bool get isConnected => _isConnected;

  // Initialize socket connection
  Future<void> connect(String userId) async {
    if (_socket?.connected == true) {
      await disconnect();
    }

    _userId = userId;
    
    _socket = IO.io(
      '${SocketConfig.serverUrl}?userId=$userId',
      IO.OptionBuilder()
          .setTransports(['websocket'])
          .enableAutoConnect()
          .setTimeout(SocketConfig.connectionTimeout.inMilliseconds)
          .build(),
    );

    _setupEventListeners();
    _socket?.connect();
  }

  void _setupEventListeners() {
    _socket?.on('connect', (data) {
      print('Connected to socket server');
      _isConnected = true;
    });

    _socket?.on('disconnect', (data) {
      print('Disconnected from socket server');
      _isConnected = false;
    });

    _socket?.on('connect_error', (error) {
      print('Socket connection error: $error');
      _isConnected = false;
    });
  }

  // Emit event to server
  void emit(String event, dynamic data) {
    if (_socket?.connected == true) {
      _socket?.emit(event, data);
      print('Emitted event: $event with data: $data');
    } else {
      print('Socket not connected, cannot emit: $event');
    }
  }

  // Listen to server events
  void on(String event, Function(dynamic) callback) {
    _socket?.on(event, callback);
  }

  // Remove event listener
  void off(String event) {
    _socket?.off(event);
  }

  // Disconnect socket
  Future<void> disconnect() async {
    if (_socket != null) {
      _socket?.disconnect();
      _socket?.dispose();
      _socket = null;
      _isConnected = false;
    }
  }
}
```

## Socket Events Overview

### Client → Server Events (Emit)
| Event Name | Purpose | Data Structure |
|------------|---------|----------------|
| `send_message` | Send a new message | ChatMessageData |
| `join_chat` | Join a chat room | JoinChatData |
| `leave_chat` | Leave a chat room | LeaveChatData |
| `messages_read` | Mark messages as read | MessagesReadData |
| `message_deleted` | Delete a message | MessageDeletedData |

### Server → Client Events (Listen)
| Event Name | Purpose | Data Structure |
|------------|---------|----------------|
| `new_message` | Receive new message | NewMessageData |
| `chat_notification` | Chat updates/notifications | ChatNotificationData |
| `messages_read` | Message read status updates | MessagesReadData |
| `send_notification` | General notifications | NotificationData |
| `receive_join` | User joined chat | UserJoinData |
| `receive_leave` | User left chat | UserLeaveData |
| `unread_count_updated` | Unread count changes | UnreadCountData |

## Data Models

### 1. User Model
```dart
// lib/models/user.dart
import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

@JsonSerializable()
class User {
  final int id;
  final String firstName;
  final String lastName;
  final String? imageUrl;

  User({
    required this.id,
    required this.firstName,
    required this.lastName,
    this.imageUrl,
  });

  String get fullName => '$firstName $lastName';
  String get initials => '${firstName[0]}${lastName[0]}'.toUpperCase();

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);
}
```

### 2. Chat Room Model
```dart
// lib/models/chat_room.dart
import 'package:json_annotation/json_annotation.dart';
import 'user.dart';

part 'chat_room.g.dart';

enum RoomType {
  @JsonValue('private')
  private,
  @JsonValue('task') 
  task,
  @JsonValue('department')
  department,
  @JsonValue('organization')
  organization,
}

@JsonSerializable()
class ChatRoom {
  final String id;
  final String name;
  final RoomType type;
  final String? lastMessage;
  final DateTime? lastMessageTime;
  final String? lastMessageType;
  final String? lastMessageStatus;
  final int unreadCount;
  final bool? isOnline;
  final String? avatar;
  final bool isBot;
  final int? botDuration;
  final List<ChatUser>? chatUsers;

  ChatRoom({
    required this.id,
    required this.name,
    required this.type,
    this.lastMessage,
    this.lastMessageTime,
    this.lastMessageType,
    this.lastMessageStatus,
    this.unreadCount = 0,
    this.isOnline,
    this.avatar,
    this.isBot = false,
    this.botDuration,
    this.chatUsers,
  });

  factory ChatRoom.fromJson(Map<String, dynamic> json) => _$ChatRoomFromJson(json);
  Map<String, dynamic> toJson() => _$ChatRoomToJson(this);
}

@JsonSerializable()
class ChatUser {
  final User user;
  final bool? isAdmin;

  ChatUser({required this.user, this.isAdmin});

  factory ChatUser.fromJson(Map<String, dynamic> json) => _$ChatUserFromJson(json);
  Map<String, dynamic> toJson() => _$ChatUserToJson(this);
}
```

### 3. Message Model
```dart
// lib/models/message.dart
import 'package:json_annotation/json_annotation.dart';
import 'user.dart';

part 'message.g.dart';

enum MessageType {
  @JsonValue('text')
  text,
  @JsonValue('image')
  image,
  @JsonValue('file')
  file,
  @JsonValue('sticker')
  sticker,
  @JsonValue('link')
  link,
}

enum MessageStatus {
  @JsonValue('sending')
  sending,
  @JsonValue('delivered')
  delivered,
  @JsonValue('read')
  read,
  @JsonValue('failed')
  failed,
}

@JsonSerializable()
class Message {
  final String id;
  final String senderId;
  final String senderName;
  final String content;
  final DateTime timestamp;
  final MessageType type;
  final MessageStatus status;
  final String? imageUrl;
  final User? user;
  final MessageReadStatus? readStatus;
  final bool? isRead;
  final DateTime? readAt;

  Message({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.content,
    required this.timestamp,
    required this.type,
    required this.status,
    this.imageUrl,
    this.user,
    this.readStatus,
    this.isRead,
    this.readAt,
  });

  factory Message.fromJson(Map<String, dynamic> json) => _$MessageFromJson(json);
  Map<String, dynamic> toJson() => _$MessageToJson(this);
}

@JsonSerializable()
class MessageReadStatus {
  final List<MessageReadUser> readByUsers;
  final List<User> unreadUsers;
  final int totalParticipants;
  final int readCount;
  final int unreadCount;

  MessageReadStatus({
    required this.readByUsers,
    required this.unreadUsers,
    required this.totalParticipants,
    required this.readCount,
    required this.unreadCount,
  });

  factory MessageReadStatus.fromJson(Map<String, dynamic> json) => 
      _$MessageReadStatusFromJson(json);
  Map<String, dynamic> toJson() => _$MessageReadStatusToJson(this);
}

@JsonSerializable()
class MessageReadUser {
  final User user;
  final DateTime readAt;

  MessageReadUser({required this.user, required this.readAt});

  factory MessageReadUser.fromJson(Map<String, dynamic> json) => 
      _$MessageReadUserFromJson(json);
  Map<String, dynamic> toJson() => _$MessageReadUserToJson(this);
}
```

## Client-Side Socket Events

### 1. Send Message
```dart
// Send a new message
void sendMessage({
  required String chatId,
  required String chatType,
  required int userId,
  required String content,
  required String messageType,
  String? optimisticId,
}) {
  final data = {
    'chatId': chatId,
    'chatType': chatType,
    'userId': userId,
    'content': content,
    'messageType': messageType,
    if (optimisticId != null) 'optimisticId': optimisticId,
  };
  
  SocketService().emit('send_message', data);
}
```

### 2. Join Chat Room
```dart
// Join a chat room
void joinChat(String chatId) {
  SocketService().emit('join_chat', {'chat_id': chatId});
}
```

### 3. Leave Chat Room
```dart
// Leave a chat room
void leaveChat(String chatId) {
  SocketService().emit('leave_chat', {'chat_id': chatId});
}
```

### 4. Mark Messages as Read
```dart
// Mark messages as read
void markMessagesAsRead({
  required String chatId,
  required int userId,
}) {
  final data = {
    'chatId': chatId,
    'userId': userId,
  };
  
  SocketService().emit('messages_read', data);
}
```

### 5. Delete Message
```dart
// Delete a message
void deleteMessage({
  required String chatId,
  required String messageId,
}) {
  final data = {
    'chatId': chatId,
    'messageId': messageId,
  };
  
  SocketService().emit('message_deleted', data);
}
```

## Server-Side Socket Events

### 1. Listen for New Messages
```dart
// lib/services/chat_socket_service.dart
class ChatSocketService {
  final SocketService _socketService = SocketService();
  
  void setupChatEventListeners({
    required Function(Message) onNewMessage,
    required Function(Map<String, dynamic>) onChatNotification,
    required Function(Map<String, dynamic>) onMessagesRead,
    required Function(Map<String, dynamic>) onUserJoined,
    required Function(Map<String, dynamic>) onUserLeft,
    required Function(Map<String, dynamic>) onUnreadCountUpdated,
    required Function(Map<String, dynamic>) onNotification,
  }) {
    // Listen for new messages
    _socketService.on('new_message', (data) {
      try {
        final message = Message.fromJson({
          'id': data['messageId'].toString(),
          'senderId': data['sender']['id'].toString(),
          'senderName': '${data['sender']['firstName']} ${data['sender']['lastName']}',
          'content': data['content'],
          'timestamp': DateTime.parse(data['createdAt']).toIso8601String(),
          'type': data['messageType'].toLowerCase(),
          'status': data['status']?.toLowerCase() ?? 'delivered',
          'user': {
            'id': data['sender']['id'],
            'firstName': data['sender']['firstName'],
            'lastName': data['sender']['lastName'],
            'imageUrl': data['sender']['imageUrl'],
          },
        });
        onNewMessage(message);
      } catch (e) {
        print('Error parsing new message: $e');
      }
    });

    // Listen for chat notifications
    _socketService.on('chat_notification', (data) {
      onChatNotification(data);
    });

    // Listen for message read updates
    _socketService.on('messages_read', (data) {
      onMessagesRead(data);
    });

    // Listen for user join events
    _socketService.on('receive_join', (data) {
      onUserJoined(data);
    });

    // Listen for user leave events
    _socketService.on('receive_leave', (data) {
      onUserLeft(data);
    });

    // Listen for unread count updates
    _socketService.on('unread_count_updated', (data) {
      onUnreadCountUpdated(data);
    });

    // Listen for general notifications
    _socketService.on('send_notification', (data) {
      onNotification(data);
    });
  }

  void removeAllListeners() {
    _socketService.off('new_message');
    _socketService.off('chat_notification');
    _socketService.off('messages_read');
    _socketService.off('receive_join');
    _socketService.off('receive_leave');
    _socketService.off('unread_count_updated');
    _socketService.off('send_notification');
  }
}
```

## Chat Message Flow

### 1. Optimistic Message Updates
```dart
// lib/blocs/chat_bloc.dart
class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final ChatSocketService _chatSocketService = ChatSocketService();
  
  void sendMessage(String content, MessageType type) {
    // Create optimistic message
    final optimisticId = 'optimistic_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
    final optimisticMessage = Message(
      id: optimisticId,
      senderId: currentUser.id.toString(),
      senderName: currentUser.fullName,
      content: content,
      timestamp: DateTime.now(),
      type: type,
      status: MessageStatus.sending,
      user: currentUser,
    );

    // Add to UI immediately
    emit(state.copyWith(
      messages: [...state.messages, optimisticMessage],
    ));

    // Send via socket with delay to prevent race conditions
    Timer(Duration(milliseconds: 100), () {
      _chatSocketService.sendMessage(
        chatId: state.selectedRoom!.id,
        chatType: state.selectedRoom!.type.name,
        userId: currentUser.id,
        content: content,
        messageType: type.name.toUpperCase(),
        optimisticId: optimisticId,
      );
    });

    // Set timeout for failed message
    Timer(Duration(seconds: 10), () {
      if (state.messages.any((m) => m.id == optimisticId && m.status == MessageStatus.sending)) {
        final updatedMessages = state.messages.map((m) {
          return m.id == optimisticId ? m.copyWith(status: MessageStatus.failed) : m;
        }).toList();
        
        emit(state.copyWith(messages: updatedMessages));
      }
    });
  }
}
```

### 2. Message Confirmation Flow
```dart
void handleNewMessage(Message newMessage) {
  // Check if this is a confirmation for an optimistic message
  final optimisticId = newMessage.optimisticId;
  
  if (optimisticId != null && newMessage.user?.id == currentUser.id) {
    // Replace optimistic message with confirmed message
    final updatedMessages = state.messages.map((m) {
      return m.id == optimisticId ? newMessage.copyWith(id: newMessage.id) : m;
    }).toList();
    
    emit(state.copyWith(messages: updatedMessages));
  } else {
    // Add new message from other users
    final messageExists = state.messages.any((m) => m.id == newMessage.id);
    if (!messageExists) {
      emit(state.copyWith(
        messages: [...state.messages, newMessage],
      ));
    }
  }
}
```

## Real-time Features

### 1. Read Status Updates
```dart
void handleMessagesRead(Map<String, dynamic> data) {
  if (data['id'] == null) return;
  
  final chatId = data['id'].toString();
  
  // Update read status for current chat
  if (state.selectedRoom?.id == chatId) {
    // Refresh messages with updated read status
    _refreshMessagesWithReadStatus();
  }
}

Future<void> _refreshMessagesWithReadStatus() async {
  try {
    final response = await chatApiService.getMessages(
      int.parse(state.selectedRoom!.id),
      limit: state.messages.length,
      includeReadStatus: true,
    );
    
    final messages = response.messages.map((msg) => Message.fromJson(msg)).toList();
    emit(state.copyWith(messages: messages));
  } catch (e) {
    print('Error refreshing messages with read status: $e');
  }
}
```

### 2. Auto-mark Messages as Read
```dart
// Using Visibility Detector or Intersection Observer equivalent
void markMessageAsReadWhenVisible(String messageId) {
  Timer(Duration(milliseconds: 500), () {
    if (!_readMessages.contains(messageId)) {
      _chatApiService.markMessageAsRead(int.parse(messageId));
      _readMessages.add(messageId);
    }
  });
}
```

### 3. Unread Count Management
```dart
void handleUnreadCountUpdate(Map<String, dynamic> data) {
  final chatType = data['chatType'] as String?;
  final count = data['count'] as int?;
  
  if (chatType != null && count != null) {
    // Update unread count for specific chat type
    final updatedRooms = state.rooms[chatType]?.map((room) {
      return room.id == data['chatId'] 
          ? room.copyWith(unreadCount: count)
          : room;
    }).toList();
    
    if (updatedRooms != null) {
      emit(state.copyWith(
        rooms: {
          ...state.rooms,
          chatType: updatedRooms,
        },
      ));
    }
  }
}
```

## Error Handling

### 1. Connection Error Handling
```dart
class SocketConnectionBloc extends Bloc<SocketConnectionEvent, SocketConnectionState> {
  int _reconnectAttempts = 0;
  Timer? _reconnectTimer;

  void _handleConnectionError() {
    if (_reconnectAttempts < SocketConfig.maxReconnectAttempts) {
      _reconnectAttempts++;
      
      emit(SocketConnectionState.reconnecting(
        attempt: _reconnectAttempts,
        maxAttempts: SocketConfig.maxReconnectAttempts,
      ));

      _reconnectTimer = Timer(
        Duration(seconds: _reconnectAttempts * 2), // Exponential backoff
        () => _attemptReconnection(),
      );
    } else {
      emit(SocketConnectionState.failed(
        error: 'Max reconnection attempts reached',
      ));
    }
  }

  void _attemptReconnection() async {
    try {
      await SocketService().connect(currentUserId);
      _reconnectAttempts = 0;
      emit(SocketConnectionState.connected());
    } catch (e) {
      _handleConnectionError();
    }
  }
}
```

### 2. Message Delivery Error Handling
```dart
void retryFailedMessage(Message failedMessage) {
  // Remove failed message
  final updatedMessages = state.messages.where((m) => m.id != failedMessage.id).toList();
  emit(state.copyWith(messages: updatedMessages));

  // Resend message
  sendMessage(failedMessage.content, failedMessage.type);
}
```

## Best Practices

### 1. State Management with BLoC
```dart
// lib/blocs/chat_state.dart
@immutable
class ChatState {
  final List<ChatRoom> rooms;
  final ChatRoom? selectedRoom;
  final List<Message> messages;
  final bool isLoading;
  final String? error;
  final Map<String, List<ChatRoom>> roomsByType;

  const ChatState({
    this.rooms = const [],
    this.selectedRoom,
    this.messages = const [],
    this.isLoading = false,
    this.error,
    this.roomsByType = const {},
  });

  ChatState copyWith({
    List<ChatRoom>? rooms,
    ChatRoom? selectedRoom,
    List<Message>? messages,
    bool? isLoading,
    String? error,
    Map<String, List<ChatRoom>>? roomsByType,
  }) {
    return ChatState(
      rooms: rooms ?? this.rooms,
      selectedRoom: selectedRoom ?? this.selectedRoom,
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      roomsByType: roomsByType ?? this.roomsByType,
    );
  }
}
```

### 2. Memory Management
```dart
class ChatScreen extends StatefulWidget {
  @override
  _ChatScreenState createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  late ChatSocketService _chatSocketService;

  @override
  void initState() {
    super.initState();
    _chatSocketService = ChatSocketService();
    _setupSocketListeners();
  }

  @override
  void dispose() {
    _chatSocketService.removeAllListeners();
    super.dispose();
  }

  void _setupSocketListeners() {
    _chatSocketService.setupChatEventListeners(
      onNewMessage: (message) => context.read<ChatBloc>().add(NewMessageReceived(message)),
      onChatNotification: (data) => context.read<ChatBloc>().add(ChatNotificationReceived(data)),
      // ... other listeners
    );
  }
}
```

### 3. Network State Handling
```dart
// lib/services/network_service.dart
class NetworkService {
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  final StreamController<bool> _connectionController = StreamController<bool>.broadcast();
  Stream<bool> get connectionStream => _connectionController.stream;

  bool _isOnline = true;
  bool get isOnline => _isOnline;

  void startNetworkMonitoring() {
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      final wasOnline = _isOnline;
      _isOnline = result != ConnectivityResult.none;
      
      if (!wasOnline && _isOnline) {
        // Reconnect socket when network is restored
        _reconnectSocket();
      }
      
      _connectionController.add(_isOnline);
    });
  }

  void _reconnectSocket() async {
    if (SocketService().isConnected) return;
    
    try {
      await SocketService().connect(currentUserId);
    } catch (e) {
      print('Failed to reconnect socket: $e');
    }
  }
}
```

## Example Implementation

### 1. Chat Screen Widget
```dart
// lib/screens/chat_screen.dart
class ChatScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocBuilder<ChatBloc, ChatState>(
        builder: (context, state) {
          return Row(
            children: [
              // Chat Sidebar
              Container(
                width: 300,
                child: ChatSidebar(
                  rooms: state.roomsByType,
                  selectedRoom: state.selectedRoom,
                  onRoomSelected: (room) {
                    context.read<ChatBloc>().add(RoomSelected(room));
                  },
                ),
              ),
              
              // Chat Main Area
              Expanded(
                child: state.selectedRoom != null
                    ? ChatMainArea(
                        room: state.selectedRoom!,
                        messages: state.messages,
                        onSendMessage: (content, type) {
                          context.read<ChatBloc>().add(
                            SendMessage(content: content, type: type),
                          );
                        },
                      )
                    : EmptyChatArea(),
              ),
            ],
          );
        },
      ),
    );
  }
}
```

### 2. Message Bubble Widget
```dart
// lib/widgets/message_bubble.dart
class MessageBubble extends StatelessWidget {
  final Message message;
  final bool isOwnMessage;

  const MessageBubble({
    Key? key,
    required this.message,
    required this.isOwnMessage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      child: Row(
        mainAxisAlignment: isOwnMessage 
            ? MainAxisAlignment.end 
            : MainAxisAlignment.start,
        children: [
          if (!isOwnMessage) ...[
            CircleAvatar(
              backgroundImage: message.user?.imageUrl != null
                  ? NetworkImage(message.user!.imageUrl!)
                  : null,
              child: message.user?.imageUrl == null
                  ? Text(message.user?.initials ?? 'U')
                  : null,
            ),
            SizedBox(width: 8),
          ],
          
          Flexible(
            child: Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isOwnMessage 
                    ? Theme.of(context).primaryColor
                    : Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.content,
                    style: TextStyle(
                      color: isOwnMessage ? Colors.white : Colors.black,
                    ),
                  ),
                  SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _formatTime(message.timestamp),
                        style: TextStyle(
                          fontSize: 10,
                          color: isOwnMessage 
                              ? Colors.white70 
                              : Colors.grey[600],
                        ),
                      ),
                      if (isOwnMessage) ...[
                        SizedBox(width: 4),
                        Icon(
                          _getStatusIcon(message.status),
                          size: 12,
                          color: Colors.white70,
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getStatusIcon(MessageStatus status) {
    switch (status) {
      case MessageStatus.sending:
        return Icons.access_time;
      case MessageStatus.delivered:
        return Icons.check;
      case MessageStatus.read:
        return Icons.done_all;
      case MessageStatus.failed:
        return Icons.error_outline;
    }
  }

  String _formatTime(DateTime timestamp) {
    return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }
}
```

This comprehensive guide provides everything needed to implement the socket.io chat functionality in Flutter, including all socket events, data models, error handling, and best practices for real-time communication.
