# Chat Unread Messages API Documentation

## สารบัญ (Table of Contents)
1. [ภาพรวม (Overview)](#ภาพรวม-overview)
2. [Authentication](#authentication)
3. [API Endpoints](#api-endpoints)
4. [Chat Types](#chat-types)
5. [Request/Response Examples](#requestresponse-examples)
6. [Error Handling](#error-handling)
7. [Real-time Updates](#real-time-updates)
8. [Implementation Guidelines](#implementation-guidelines)

## ภาพรวม (Overview)

API นี้ให้บริการข้อมูลเกี่ยวกับข้อความที่ยังไม่ได้อ่านในแชท รองรับหลายประเภทของแชทและให้ข้อมูลแบบละเอียดสำหรับการนำไปใช้งานในแอปพลิเคชัน

### Features
- ดูจำนวนข้อความที่ยังไม่ได้อ่านในแต่ละแชท
- รองรับ Chat Types: PRIVATE, TASK, DEPARTMENT, ORGANIZATION
- ข้อมูลแบบ Real-time
- API แบบ RESTful
- การจัดการสิทธิ์การเข้าถึงที่ปลอดภัย

### Base URL
```
https://your-domain.com/api/v1
```

## Authentication

ทุก API endpoint ต้องการ Authentication ผ่าน Bearer Token

### Header Format
```http
Authorization: Bearer {access_token}
Content-Type: application/json
```

### Token Requirements
- JWT Token ที่ถูกต้อง
- มี scope `chat:read` สำหรับการอ่านข้อมูล
- มี scope `chat:write` สำหรับการอัปเดตสถานะ

## API Endpoints

### 1. Get Unread Count for Specific Chat
ดูจำนวนข้อความที่ยังไม่ได้อ่านในแชทเฉพาะ

**Endpoint:** `GET /chat-message/unread-count`

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `chatId` | number | Yes | รหัสของแชทที่ต้องการดูข้อมูล |

**Request Example:**
```bash
curl -X GET "https://your-domain.com/api/v1/chat-message/unread-count?chatId=123" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "chatId": 123,
  "unreadCount": 5
}
```

### 2. Get Unread Counts for All User's Chats
ดูจำนวนข้อความที่ยังไม่ได้อ่านในทุกแชทของผู้ใช้

**Endpoint:** `GET /chat-message/unread-count`

**Request Example:**
```bash
curl -X GET "https://your-domain.com/api/v1/chat-message/unread-count" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "unreadCounts": [
    {
      "chatId": 123,
      "chatName": "John Doe",
      "chatType": "PRIVATE",
      "unreadCount": 5
    },
    {
      "chatId": 456,
      "chatName": "Development Team",
      "chatType": "DEPARTMENT",
      "unreadCount": 12
    },
    {
      "chatId": 789,
      "chatName": "Project Alpha",
      "chatType": "TASK",
      "unreadCount": 3
    },
    {
      "chatId": 101,
      "chatName": "Company Updates",
      "chatType": "ORGANIZATION",
      "unreadCount": 0
    }
  ]
}
```

### 3. Get Total Unread Count
ดูจำนวนข้อความที่ยังไม่ได้อ่านทั้งหมดจากทุกแชท

**Endpoint:** `GET /chat-message/unread-count`

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `total` | boolean | Yes | ตั้งค่าเป็น `true` เพื่อดูยอดรวม |

**Request Example:**
```bash
curl -X GET "https://your-domain.com/api/v1/chat-message/unread-count?total=true" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "totalUnreadCount": 20
}
```

### 4. Get Chat List with Unread Information
ดูรายการแชททั้งหมดพร้อมข้อมูลข้อความที่ยังไม่ได้อ่าน

**Endpoint:** `GET /chat`

**Query Parameters:**
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `chatType` | string | No | กรองตามประเภทแชท (PRIVATE, TASK, DEPARTMENT, ORGANIZATION) |
| `organizationId` | number | No | กรองตาม Organization ID |
| `departmentId` | number | No | กรองตาม Department ID |
| `taskId` | number | No | กรองตาม Task ID |

**Request Example:**
```bash
curl -X GET "https://your-domain.com/api/v1/chat?chatType=PRIVATE" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json"
```

**Response:**
```json
{
  "chats": [
    {
      "id": 123,
      "name": "John Doe",
      "chatType": "PRIVATE",
      "createdAt": "2024-01-01T10:00:00.000Z",
      "updatedAt": "2024-01-01T15:30:00.000Z",
      "organization": null,
      "department": null,
      "task": null,
      "chatUsers": [
        {
          "userId": 1,
          "joinedAt": "2024-01-01T10:00:00.000Z",
          "user": {
            "id": 1,
            "firstName": "Current",
            "lastName": "User",
            "email": "<EMAIL>",
            "imageUrl": "https://example.com/avatar1.jpg"
          }
        },
        {
          "userId": 2,
          "joinedAt": "2024-01-01T10:00:00.000Z",
          "user": {
            "id": 2,
            "firstName": "John",
            "lastName": "Doe",
            "email": "<EMAIL>",
            "imageUrl": "https://example.com/avatar2.jpg"
          }
        }
      ],
      "messages": [
        {
          "id": 567,
          "content": "Hello! How are you today?",
          "messageType": "TEXT",
          "createdAt": "2024-01-01T15:30:00.000Z",
          "user": {
            "id": 2,
            "firstName": "John",
            "lastName": "Doe",
            "imageUrl": "https://example.com/avatar2.jpg"
          }
        }
      ],
      "_count": {
        "messages": 25,
        "chatUsers": 2
      },
      "unreadCount": 5
    }
  ]
}
```

### 5. Mark Messages as Read
อัปเดตสถานะข้อความเป็น "อ่านแล้ว"

**Endpoint:** `POST /chat-message/mark-read`

**Request Body Options:**

#### Mark Single Message as Read:
```json
{
  "messageId": 567
}
```

#### Mark All Messages in Chat as Read:
```json
{
  "chatId": 123
}
```

#### Mark Messages Before Specific Message as Read:
```json
{
  "chatId": 123,
  "beforeMessageId": 567
}
```

**Request Example:**
```bash
curl -X POST "https://your-domain.com/api/v1/chat-message/mark-read" \
  -H "Authorization: Bearer your_access_token" \
  -H "Content-Type: application/json" \
  -d '{"chatId": 123}'
```

**Response:**
```json
{
  "message": "Messages marked as read",
  "markedCount": 5,
  "totalMessages": 5
}
```

## Chat Types

### 1. PRIVATE Chats
แชทส่วนตัวระหว่างผู้ใช้ 2 คน

**Characteristics:**
- `chatType`: "PRIVATE"
- ผู้เข้าร่วม: ผู้ใช้ 2 คนเท่านั้น
- การสร้าง: ผู้ใช้เลือกสมาชิกที่ต้องการคุยด้วย
- ความเป็นส่วนตัว: สูง

**Example:**
```json
{
  "id": 123,
  "name": "John Doe",
  "chatType": "PRIVATE",
  "organizationId": null,
  "departmentId": null,
  "taskId": null
}
```

### 2. TASK Chats
แชทที่เกี่ยวข้องกับงาน/โปรเจกต์เฉพาะ

**Characteristics:**
- `chatType`: "TASK"
- ผู้เข้าร่วม: ผู้สร้างงาน + ผู้ที่ได้รับมอบหมาย + ผู้ที่เกี่ยวข้อง
- การสร้าง: สร้างอัตโนมัติเมื่อมีการสร้างงาน หรือสร้างโดยผู้ใช้
- เชื่อมโยงกับ: Task ID

**Example:**
```json
{
  "id": 456,
  "name": "Project Alpha - Task Discussion",
  "chatType": "TASK",
  "organizationId": 1,
  "departmentId": null,
  "taskId": 789,
  "task": {
    "id": 789,
    "taskTitle": "Implement User Authentication",
    "taskDescription": "Add JWT-based authentication system"
  }
}
```

### 3. DEPARTMENT Chats
แชทระดับแผนก

**Characteristics:**
- `chatType`: "DEPARTMENT"
- ผู้เข้าร่วม: สมาชิกทั้งหมดในแผนก
- การสร้าง: สร้างอัตโนมัติสำหรับทุกแผนก หรือสร้างโดย Admin
- เชื่อมโยงกับ: Department ID

**Example:**
```json
{
  "id": 789,
  "name": "Development Team",
  "chatType": "DEPARTMENT",
  "organizationId": 1,
  "departmentId": 5,
  "taskId": null,
  "department": {
    "id": 5,
    "name": "Engineering Department",
    "description": "Software development team"
  }
}
```

### 4. ORGANIZATION Chats
แชทระดับองค์กร

**Characteristics:**
- `chatType`: "ORGANIZATION"
- ผู้เข้าร่วม: สมาชิกทั้งหมดในองค์กร
- การสร้าง: สร้างโดย Owner/Admin เท่านั้น
- เชื่อมโยงกับ: Organization ID

**Example:**
```json
{
  "id": 101,
  "name": "Company-wide Announcements",
  "chatType": "ORGANIZATION",
  "organizationId": 1,
  "departmentId": null,
  "taskId": null,
  "organization": {
    "id": 1,
    "name": "Tech Corp Ltd",
    "description": "Technology solutions company"
  }
}
```

## Request/Response Examples

### Complete Chat List Response with Unread Counts

**Request:**
```bash
curl -X GET "https://your-domain.com/api/v1/chat" \
  -H "Authorization: Bearer your_access_token"
```

**Response:**
```json
{
  "chats": [
    {
      "id": 123,
      "name": "John Doe",
      "chatType": "PRIVATE",
      "createdAt": "2024-01-01T10:00:00.000Z",
      "updatedAt": "2024-01-01T15:30:00.000Z",
      "organization": null,
      "department": null,
      "task": null,
      "chatUsers": [
        {
          "userId": 1,
          "joinedAt": "2024-01-01T10:00:00.000Z",
          "user": {
            "id": 1,
            "firstName": "Current",
            "lastName": "User",
            "imageUrl": "https://example.com/avatar1.jpg"
          }
        },
        {
          "userId": 2,
          "joinedAt": "2024-01-01T10:00:00.000Z",
          "user": {
            "id": 2,
            "firstName": "John",
            "lastName": "Doe",
            "imageUrl": "https://example.com/avatar2.jpg"
          }
        }
      ],
      "messages": [
        {
          "id": 567,
          "content": "Hello! How are you today?",
          "messageType": "TEXT",
          "createdAt": "2024-01-01T15:30:00.000Z",
          "user": {
            "id": 2,
            "firstName": "John",
            "lastName": "Doe",
            "imageUrl": "https://example.com/avatar2.jpg"
          }
        }
      ],
      "_count": {
        "messages": 25,
        "chatUsers": 2
      }
    },
    {
      "id": 456,
      "name": "Development Team",
      "chatType": "DEPARTMENT",
      "createdAt": "2024-01-01T09:00:00.000Z",
      "updatedAt": "2024-01-01T16:45:00.000Z",
      "organization": {
        "id": 1,
        "name": "Tech Corp Ltd"
      },
      "department": {
        "id": 5,
        "name": "Engineering Department"
      },
      "task": null,
      "chatUsers": [
        {
          "userId": 1,
          "joinedAt": "2024-01-01T09:00:00.000Z",
          "user": {
            "id": 1,
            "firstName": "Current",
            "lastName": "User",
            "imageUrl": "https://example.com/avatar1.jpg"
          }
        },
        {
          "userId": 3,
          "joinedAt": "2024-01-01T09:00:00.000Z",
          "user": {
            "id": 3,
            "firstName": "Alice",
            "lastName": "Smith",
            "imageUrl": "https://example.com/avatar3.jpg"
          }
        },
        {
          "userId": 4,
          "joinedAt": "2024-01-01T09:00:00.000Z",
          "user": {
            "id": 4,
            "firstName": "Bob",
            "lastName": "Wilson",
            "imageUrl": "https://example.com/avatar4.jpg"
          }
        }
      ],
      "messages": [
        {
          "id": 890,
          "content": "Team meeting at 3 PM today",
          "messageType": "TEXT",
          "createdAt": "2024-01-01T16:45:00.000Z",
          "user": {
            "id": 3,
            "firstName": "Alice",
            "lastName": "Smith",
            "imageUrl": "https://example.com/avatar3.jpg"
          }
        }
      ],
      "_count": {
        "messages": 156,
        "chatUsers": 8
      }
    }
  ]
}
```

### Unread Counts with Chat Type Filtering

**Request (แยกตาม Chat Type):**
```bash
# Private chats only
curl -X GET "https://your-domain.com/api/v1/chat?chatType=PRIVATE" \
  -H "Authorization: Bearer your_access_token"

# Department chats only
curl -X GET "https://your-domain.com/api/v1/chat?chatType=DEPARTMENT" \
  -H "Authorization: Bearer your_access_token"

# Task chats only
curl -X GET "https://your-domain.com/api/v1/chat?chatType=TASK" \
  -H "Authorization: Bearer your_access_token"

# Organization chats only
curl -X GET "https://your-domain.com/api/v1/chat?chatType=ORGANIZATION" \
  -H "Authorization: Bearer your_access_token"
```

### Combined Unread Count Summary

**Request:**
```bash
curl -X GET "https://your-domain.com/api/v1/chat-message/unread-count" \
  -H "Authorization: Bearer your_access_token"
```

**Response (ตัวอย่างข้อมูลแยกตาม Chat Type):**
```json
{
  "unreadCounts": [
    {
      "chatId": 123,
      "chatName": "John Doe",
      "chatType": "PRIVATE",
      "unreadCount": 5
    },
    {
      "chatId": 124,
      "chatName": "Jane Smith",
      "chatType": "PRIVATE",
      "unreadCount": 2
    },
    {
      "chatId": 456,
      "chatName": "Development Team",
      "chatType": "DEPARTMENT",
      "unreadCount": 12
    },
    {
      "chatId": 457,
      "chatName": "Marketing Team",
      "chatType": "DEPARTMENT",
      "unreadCount": 0
    },
    {
      "chatId": 789,
      "chatName": "Project Alpha",
      "chatType": "TASK",
      "unreadCount": 3
    },
    {
      "chatId": 790,
      "chatName": "Bug Fix Sprint",
      "chatType": "TASK",
      "unreadCount": 1
    },
    {
      "chatId": 101,
      "chatName": "Company Updates",
      "chatType": "ORGANIZATION",
      "unreadCount": 0
    }
  ]
}
```

## Error Handling

### Standard Error Response Format
```json
{
  "error": "Error message description",
  "code": "ERROR_CODE",
  "details": {
    "field": "additional error details"
  }
}
```

### Common Error Codes

| HTTP Status | Error Code | Description |
|-------------|------------|-------------|
| 400 | `INVALID_CHAT_ID` | Chat ID ไม่ถูกต้อง |
| 400 | `INVALID_MESSAGE_ID` | Message ID ไม่ถูกต้อง |
| 400 | `INVALID_CHAT_TYPE` | Chat Type ไม่ถูกต้อง |
| 401 | `UNAUTHORIZED` | Token ไม่ถูกต้องหรือหมดอายุ |
| 403 | `FORBIDDEN` | ไม่มีสิทธิ์เข้าถึงแชทนี้ |
| 403 | `NOT_PARTICIPANT` | ไม่ใช่สมาชิกของแชทนี้ |
| 404 | `CHAT_NOT_FOUND` | ไม่พบแชทที่ระบุ |
| 404 | `MESSAGE_NOT_FOUND` | ไม่พบข้อความที่ระบุ |
| 429 | `RATE_LIMITED` | Request มากเกินไป |
| 500 | `INTERNAL_ERROR` | ข้อผิดพลาดภายในเซิร์ฟเวอร์ |

### Error Examples

**401 Unauthorized:**
```json
{
  "error": "Invalid or expired token",
  "code": "UNAUTHORIZED"
}
```

**403 Forbidden:**
```json
{
  "error": "You are not a participant in this chat",
  "code": "NOT_PARTICIPANT",
  "details": {
    "chatId": 123,
    "userId": 456
  }
}
```

**404 Not Found:**
```json
{
  "error": "Chat not found",
  "code": "CHAT_NOT_FOUND",
  "details": {
    "chatId": 999
  }
}
```

**400 Bad Request:**
```json
{
  "error": "Invalid chat type. Must be one of: PRIVATE, TASK, DEPARTMENT, ORGANIZATION",
  "code": "INVALID_CHAT_TYPE",
  "details": {
    "provided": "GROUP",
    "allowed": ["PRIVATE", "TASK", "DEPARTMENT", "ORGANIZATION"]
  }
}
```

## Real-time Updates

### WebSocket Events

การใช้งาน WebSocket สำหรับการอัปเดตแบบ Real-time

**Connection:**
```javascript
const socket = new WebSocket('wss://your-domain.com/ws');
socket.headers = {
  'Authorization': 'Bearer your_access_token'
};
```

### Event Types

#### 1. Unread Count Updated
เมื่อจำนวนข้อความที่ยังไม่ได้อ่านมีการเปลี่ยนแปลง

**Event:** `unread_count_updated`

**Payload:**
```json
{
  "type": "unread_count_updated",
  "data": {
    "chatId": 123,
    "chatType": "PRIVATE",
    "unreadCount": 7,
    "totalUnreadCount": 25,
    "userId": 456,
    "timestamp": "2024-01-01T16:30:00.000Z"
  }
}
```

#### 2. New Message
เมื่อมีข้อความใหม่

**Event:** `new_message`

**Payload:**
```json
{
  "type": "new_message",
  "data": {
    "messageId": 567,
    "chatId": 123,
    "chatType": "PRIVATE",
    "senderId": 789,
    "content": "Hello World!",
    "messageType": "TEXT",
    "timestamp": "2024-01-01T16:30:00.000Z"
  }
}
```

#### 3. Messages Read
เมื่อมีการอ่านข้อความ

**Event:** `messages_read`

**Payload:**
```json
{
  "type": "messages_read",
  "data": {
    "chatId": 123,
    "userId": 456,
    "readMessageIds": [567, 568, 569],
    "readCount": 3,
    "timestamp": "2024-01-01T16:30:00.000Z"
  }
}
```

### JavaScript Implementation Example

```javascript
class ChatUnreadService {
  constructor(wsUrl, token) {
    this.wsUrl = wsUrl;
    this.token = token;
    this.socket = null;
    this.unreadCounts = new Map();
    this.listeners = new Map();
  }

  connect() {
    this.socket = new WebSocket(this.wsUrl);
    
    this.socket.onopen = () => {
      // Send authentication
      this.socket.send(JSON.stringify({
        type: 'auth',
        token: this.token
      }));
    };

    this.socket.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleMessage(message);
    };

    this.socket.onclose = () => {
      // Reconnect logic
      setTimeout(() => this.connect(), 5000);
    };
  }

  handleMessage(message) {
    switch (message.type) {
      case 'unread_count_updated':
        this.updateUnreadCount(message.data);
        break;
      case 'new_message':
        this.handleNewMessage(message.data);
        break;
      case 'messages_read':
        this.handleMessagesRead(message.data);
        break;
    }
  }

  updateUnreadCount(data) {
    this.unreadCounts.set(data.chatId, data.unreadCount);
    this.emit('unreadCountChanged', data);
  }

  handleNewMessage(data) {
    // Update unread count for the chat
    const currentCount = this.unreadCounts.get(data.chatId) || 0;
    this.unreadCounts.set(data.chatId, currentCount + 1);
    this.emit('newMessage', data);
  }

  handleMessagesRead(data) {
    // Update unread count
    const currentCount = this.unreadCounts.get(data.chatId) || 0;
    const newCount = Math.max(0, currentCount - data.readCount);
    this.unreadCounts.set(data.chatId, newCount);
    this.emit('messagesRead', data);
  }

  // Event listener management
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => callback(data));
    }
  }

  // API methods
  async getUnreadCounts() {
    const response = await fetch('/api/v1/chat-message/unread-count', {
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) throw new Error('Failed to fetch unread counts');
    
    const data = await response.json();
    data.unreadCounts.forEach(item => {
      this.unreadCounts.set(item.chatId, item.unreadCount);
    });
    
    return data;
  }

  async markAsRead(chatId, messageId = null) {
    const body = messageId ? { messageId } : { chatId };
    
    const response = await fetch('/api/v1/chat-message/mark-read', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(body)
    });
    
    if (!response.ok) throw new Error('Failed to mark as read');
    
    return await response.json();
  }

  getUnreadCount(chatId) {
    return this.unreadCounts.get(chatId) || 0;
  }

  getTotalUnreadCount() {
    return Array.from(this.unreadCounts.values()).reduce((sum, count) => sum + count, 0);
  }
}

// Usage example
const chatService = new ChatUnreadService('wss://your-domain.com/ws', 'your_token');

chatService.on('unreadCountChanged', (data) => {
  console.log(`Chat ${data.chatId} unread count: ${data.unreadCount}`);
  updateChatBadge(data.chatId, data.unreadCount);
});

chatService.on('newMessage', (data) => {
  console.log(`New message in chat ${data.chatId}`);
  showNotification(data);
});

chatService.connect();
```

## Implementation Guidelines

### Frontend Implementation

#### 1. Chat List with Unread Badges
```javascript
// React Component Example
import { useState, useEffect } from 'react';

function ChatList() {
  const [chats, setChats] = useState([]);
  const [unreadCounts, setUnreadCounts] = useState(new Map());

  useEffect(() => {
    fetchChats();
    fetchUnreadCounts();
  }, []);

  const fetchChats = async () => {
    const response = await fetch('/api/v1/chat', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const data = await response.json();
    setChats(data.chats);
  };

  const fetchUnreadCounts = async () => {
    const response = await fetch('/api/v1/chat-message/unread-count', {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    const data = await response.json();
    
    const countMap = new Map();
    data.unreadCounts.forEach(item => {
      countMap.set(item.chatId, item.unreadCount);
    });
    setUnreadCounts(countMap);
  };

  const getChatTypeIcon = (chatType) => {
    const icons = {
      'PRIVATE': '👤',
      'TASK': '📋',
      'DEPARTMENT': '🏢',
      'ORGANIZATION': '🌐'
    };
    return icons[chatType] || '💬';
  };

  const getChatTypeName = (chatType) => {
    const names = {
      'PRIVATE': 'แชทส่วนตัว',
      'TASK': 'แชทงาน',
      'DEPARTMENT': 'แชทแผนก',
      'ORGANIZATION': 'แชทองค์กร'
    };
    return names[chatType] || 'แชท';
  };

  return (
    <div className="chat-list">
      {chats.map(chat => (
        <div key={chat.id} className={`chat-item ${chat.chatType.toLowerCase()}`}>
          <div className="chat-icon">
            {getChatTypeIcon(chat.chatType)}
          </div>
          <div className="chat-info">
            <h3>{chat.name}</h3>
            <p className="chat-type">{getChatTypeName(chat.chatType)}</p>
            {chat.messages.length > 0 && (
              <p className="last-message">{chat.messages[0].content}</p>
            )}
          </div>
          {unreadCounts.get(chat.id) > 0 && (
            <div className="unread-badge">
              {unreadCounts.get(chat.id)}
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
```

#### 2. CSS Styling for Chat Types
```css
.chat-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 16px;
}

.chat-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  border-left: 4px solid transparent;
  transition: all 0.2s ease;
}

.chat-item:hover {
  background-color: #f5f5f5;
}

.chat-item.private {
  border-left-color: #007bff;
}

.chat-item.task {
  border-left-color: #28a745;
}

.chat-item.department {
  border-left-color: #ffc107;
}

.chat-item.organization {
  border-left-color: #dc3545;
}

.chat-icon {
  font-size: 24px;
  margin-right: 12px;
}

.chat-info {
  flex: 1;
}

.chat-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.chat-type {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
}

.last-message {
  margin: 0;
  font-size: 14px;
  color: #888;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.unread-badge {
  background-color: #ff4757;
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}
```

#### 3. Tab Navigation by Chat Type
```javascript
function ChatTabs() {
  const [activeTab, setActiveTab] = useState('ALL');
  const [chats, setChats] = useState([]);
  const [unreadCounts, setUnreadCounts] = useState(new Map());

  const tabs = [
    { id: 'ALL', name: 'ทั้งหมด', type: null },
    { id: 'PRIVATE', name: 'แชทส่วนตัว', type: 'PRIVATE' },
    { id: 'TASK', name: 'แชทงาน', type: 'TASK' },
    { id: 'DEPARTMENT', name: 'แชทแผนก', type: 'DEPARTMENT' },
    { id: 'ORGANIZATION', name: 'แชทองค์กร', type: 'ORGANIZATION' }
  ];

  const getUnreadCountForType = (chatType) => {
    if (!chatType) {
      // All chats
      return Array.from(unreadCounts.values()).reduce((sum, count) => sum + count, 0);
    }
    
    return chats
      .filter(chat => chat.chatType === chatType)
      .reduce((sum, chat) => sum + (unreadCounts.get(chat.id) || 0), 0);
  };

  const getFilteredChats = () => {
    if (activeTab === 'ALL') return chats;
    return chats.filter(chat => chat.chatType === activeTab);
  };

  return (
    <div className="chat-tabs">
      <div className="tab-navigation">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.name}
            {getUnreadCountForType(tab.type) > 0 && (
              <span className="tab-badge">
                {getUnreadCountForType(tab.type)}
              </span>
            )}
          </button>
        ))}
      </div>
      
      <div className="tab-content">
        <ChatList chats={getFilteredChats()} unreadCounts={unreadCounts} />
      </div>
    </div>
  );
}
```

### Backend Security Considerations

#### 1. Access Control
```typescript
// Example middleware for chat access control
async function validateChatAccess(chatId: number, userId: number) {
  const chatUser = await prisma.chatUser.findUnique({
    where: {
      chatId_userId: {
        chatId,
        userId
      }
    },
    include: {
      chat: {
        select: {
          chatType: true,
          organizationId: true,
          departmentId: true,
          taskId: true
        }
      }
    }
  });

  if (!chatUser) {
    throw new Error('User is not a participant in this chat');
  }

  return chatUser;
}
```

#### 2. Rate Limiting
```typescript
// Example rate limiting for unread count API
const rateLimiter = new Map();

function checkRateLimit(userId: number, limit: number = 60, window: number = 60000) {
  const now = Date.now();
  const userRequests = rateLimiter.get(userId) || [];
  
  // Remove old requests outside the window
  const validRequests = userRequests.filter(time => now - time < window);
  
  if (validRequests.length >= limit) {
    throw new Error('Rate limit exceeded');
  }
  
  validRequests.push(now);
  rateLimiter.set(userId, validRequests);
}
```

### Performance Optimization

#### 1. Database Indexing
```sql
-- Recommended indexes for optimal performance
CREATE INDEX idx_message_reads_user_id_read_at ON message_reads(user_id, read_at);
CREATE INDEX idx_message_reads_message_id ON message_reads(message_id);
CREATE INDEX idx_chat_messages_chat_id_created_at ON chat_messages(chat_id, created_at);
CREATE INDEX idx_chat_users_user_id ON chat_users(user_id);
CREATE INDEX idx_chat_users_chat_id ON chat_users(chat_id);
CREATE INDEX idx_chats_chat_type ON chats(chat_type);
```

#### 2. Caching Strategy
```typescript
// Redis caching example
class UnreadCountCache {
  private redis: Redis;
  private readonly TTL = 300; // 5 minutes

  async getUnreadCount(userId: number, chatId: number): Promise<number | null> {
    const key = `unread:${userId}:${chatId}`;
    const cached = await this.redis.get(key);
    return cached ? parseInt(cached) : null;
  }

  async setUnreadCount(userId: number, chatId: number, count: number): Promise<void> {
    const key = `unread:${userId}:${chatId}`;
    await this.redis.setex(key, this.TTL, count.toString());
  }

  async invalidateUnreadCount(userId: number, chatId: number): Promise<void> {
    const key = `unread:${userId}:${chatId}`;
    await this.redis.del(key);
  }
}
```

### Testing Examples

#### 1. Unit Tests
```typescript
describe('Unread Count API', () => {
  test('should return unread count for specific chat', async () => {
    const response = await request(app)
      .get('/api/v1/chat-message/unread-count?chatId=123')
      .set('Authorization', `Bearer ${validToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('chatId', 123);
    expect(response.body).toHaveProperty('unreadCount');
    expect(typeof response.body.unreadCount).toBe('number');
  });

  test('should return all unread counts for user', async () => {
    const response = await request(app)
      .get('/api/v1/chat-message/unread-count')
      .set('Authorization', `Bearer ${validToken}`);

    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('unreadCounts');
    expect(Array.isArray(response.body.unreadCounts)).toBe(true);
  });

  test('should return 403 for chat user is not participant in', async () => {
    const response = await request(app)
      .get('/api/v1/chat-message/unread-count?chatId=999')
      .set('Authorization', `Bearer ${validToken}`);

    expect(response.status).toBe(403);
    expect(response.body.error).toContain('not a participant');
  });
});
```

#### 2. Integration Tests
```typescript
describe('Chat Types Integration', () => {
  test('should filter chats by type correctly', async () => {
    // Test private chats
    const privateResponse = await request(app)
      .get('/api/v1/chat?chatType=PRIVATE')
      .set('Authorization', `Bearer ${validToken}`);

    expect(privateResponse.status).toBe(200);
    privateResponse.body.chats.forEach(chat => {
      expect(chat.chatType).toBe('PRIVATE');
    });

    // Test department chats
    const deptResponse = await request(app)
      .get('/api/v1/chat?chatType=DEPARTMENT')
      .set('Authorization', `Bearer ${validToken}`);

    expect(deptResponse.status).toBe(200);
    deptResponse.body.chats.forEach(chat => {
      expect(chat.chatType).toBe('DEPARTMENT');
    });
  });
});
```

## สรุป (Summary)

เอกสารนี้ให้ข้อมูลครบถ้วนเกี่ยวกับ API สำหรับการจัดการข้อความที่ยังไม่ได้อ่านในระบบแชท รองรับทุกประเภทของแชท (PRIVATE, TASK, DEPARTMENT, ORGANIZATION) และมีระบบ Real-time updates ที่สมบูรณ์

### Key Features:
- ✅ API ครบถ้วนสำหรับการดูและจัดการข้อความที่ยังไม่ได้อ่าน
- ✅ รองรับ Chat Types ทั้ง 4 ประเภท
- ✅ Real-time updates ผ่าน WebSocket
- ✅ Security และ Access Control ที่เข้มงวด
- ✅ Error Handling ที่ครอบคลุม
- ✅ Performance Optimization
- ✅ ตัวอย่างการ Implementation ที่สมบูรณ์

### การใช้งาน:
1. ใช้ `/chat-message/unread-count` สำหรับดูจำนวนข้อความที่ยังไม่ได้อ่าน
2. ใช้ `/chat` สำหรับดูรายการแชททั้งหมดพร้อมข้อมูลแยกตาม Chat Type
3. ใช้ `/chat-message/mark-read` สำหรับอัปเดตสถานะการอ่าน
4. ใช้ WebSocket สำหรับรับข้อมูลแบบ Real-time

เอกสารนี้สามารถนำไปใช้เป็นคู่มือการพัฒนาและการใช้งาน API ได้ทันที
